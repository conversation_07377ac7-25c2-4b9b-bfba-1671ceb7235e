import matplotlib.pyplot as plt
import matplotlib
import numpy as np
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple


class TrainingVisualizer:
    """训练过程可视化器"""
    
    def __init__(self, save_dir: str, use_chinese_font: bool = False):
        """
        初始化可视化器
        
        Args:
            save_dir: 保存目录
            use_chinese_font: 是否使用中文字体
        """
        self.save_dir = save_dir
        self.use_chinese_font = use_chinese_font
        
        # 确保保存目录存在
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置字体
        self._setup_fonts()
        
        # 预定义颜色方案
        self.colors = {
            'train': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'],
            'val': ['#17becf', '#bcbd22', '#8c564b', '#e377c2', '#7f7f7f'],
            'single': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                      '#17becf', '#bcbd22', '#8c564b', '#e377c2']
        }
    
    def _setup_fonts(self):
        """设置字体配置"""
        if self.use_chinese_font:
            try:
                matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial']
                matplotlib.rcParams['axes.unicode_minus'] = False
            except:
                logging.warning("中文字体设置失败，使用英文字体")
                matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
        else:
            matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
            matplotlib.rcParams['axes.unicode_minus'] = False
    
    def plot_training_curves(self, history: Dict, timestamp: Optional[str] = None, 
                           figsize: Tuple[int, int] = (20, 15)) -> str:
        """
        绘制完整的训练曲线
        
        Args:
            history: 训练历史数据字典
            timestamp: 时间戳，如果为None则自动生成
            figsize: 图形大小
            
        Returns:
            保存的图片路径
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            fig, axes = plt.subplots(3, 3, figsize=figsize)
            fig.suptitle('Training Progress Dashboard', fontsize=16, y=0.98)
            
            # 绘制各个子图
            self._plot_generator_loss(axes[0, 0], history)
            self._plot_discriminator_hr_loss(axes[0, 1], history)
            self._plot_discriminator_lr_loss(axes[0, 2], history)
            self._plot_cycle_loss(axes[1, 0], history)
            self._plot_psnr(axes[1, 1], history)
            self._plot_ssim(axes[1, 2], history)
            self._plot_learning_rates(axes[2, 0], history)
            self._plot_loss_comparison(axes[2, 1], history)
            self._plot_validation_trends(axes[2, 2], history)
            
            plt.tight_layout(rect=[0, 0.03, 1, 0.95])
            
            # 保存图像
            plot_path = os.path.join(self.save_dir, f'training_curves_{timestamp}.png')
            plt.savefig(plot_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logging.info(f'Training curves saved to: {plot_path}')
            return plot_path
            
        except Exception as e:
            logging.error(f'Error plotting training curves: {str(e)}')
            return self._create_fallback_plot(history, timestamp)
    
    def plot_loss_only(self, history: Dict, timestamp: Optional[str] = None) -> str:
        """
        只绘制损失曲线
        
        Args:
            history: 训练历史数据
            timestamp: 时间戳
            
        Returns:
            保存的图片路径
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Training Loss Curves', fontsize=14)
            
            self._plot_generator_loss(axes[0, 0], history)
            self._plot_discriminator_hr_loss(axes[0, 1], history)
            self._plot_discriminator_lr_loss(axes[1, 0], history)
            self._plot_cycle_loss(axes[1, 1], history)
            
            plt.tight_layout(rect=[0, 0.03, 1, 0.97])
            
            plot_path = os.path.join(self.save_dir, f'loss_curves_{timestamp}.png')
            plt.savefig(plot_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logging.info(f'Loss curves saved to: {plot_path}')
            return plot_path
            
        except Exception as e:
            logging.error(f'Error plotting loss curves: {str(e)}')
            return ""
    
    def plot_metrics_only(self, history: Dict, timestamp: Optional[str] = None) -> str:
        """
        只绘制评估指标
        
        Args:
            history: 训练历史数据
            timestamp: 时间戳
            
        Returns:
            保存的图片路径
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            fig, axes = plt.subplots(1, 2, figsize=(12, 5))
            fig.suptitle('Evaluation Metrics', fontsize=14)
            
            self._plot_psnr(axes[0], history)
            self._plot_ssim(axes[1], history)
            
            plt.tight_layout(rect=[0, 0.03, 1, 0.97])
            
            plot_path = os.path.join(self.save_dir, f'metrics_{timestamp}.png')
            plt.savefig(plot_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logging.info(f'Metrics saved to: {plot_path}')
            return plot_path
            
        except Exception as e:
            logging.error(f'Error plotting metrics: {str(e)}')
            return ""
    
    def _plot_generator_loss(self, ax, history: Dict):
        """绘制生成器损失"""
        title = "Generator Loss" if not self.use_chinese_font else "生成器损失"
        self._plot_train_val_curve(ax, history, 'train_g_loss', 'val_g_loss', 
                                 'G Loss', title, self.colors['train'][0], self.colors['val'][0])
    
    def _plot_discriminator_hr_loss(self, ax, history: Dict):
        """绘制HR判别器损失"""
        title = "HR Discriminator Loss" if not self.use_chinese_font else "高分辨率判别器损失"
        self._plot_train_val_curve(ax, history, 'train_d_hr_loss', 'val_d_hr_loss', 
                                 'D_HR Loss', title, self.colors['train'][1], self.colors['val'][1])
    
    def _plot_discriminator_lr_loss(self, ax, history: Dict):
        """绘制LR判别器损失"""
        title = "LR Discriminator Loss" if not self.use_chinese_font else "低分辨率判别器损失"
        self._plot_train_val_curve(ax, history, 'train_d_lr_loss', 'val_d_lr_loss', 
                                 'D_LR Loss', title, self.colors['train'][2], self.colors['val'][2])
    
    def _plot_cycle_loss(self, ax, history: Dict):
        """绘制循环一致性损失"""
        title = "Cycle Consistency Loss" if not self.use_chinese_font else "循环一致性损失"
        self._plot_train_val_curve(ax, history, 'train_cycle_loss', 'val_cycle_loss', 
                                 'Cycle Loss', title, self.colors['train'][3], self.colors['val'][3])
    
    def _plot_psnr(self, ax, history: Dict):
        """绘制PSNR"""
        title = "Peak Signal-to-Noise Ratio" if not self.use_chinese_font else "峰值信噪比"
        if 'val_psnr' in history and len(history['val_psnr']) > 0:
            epochs = range(1, len(history['val_psnr']) + 1)
            ax.plot(epochs, history['val_psnr'], label='Val PSNR', 
                   color=self.colors['single'][0], marker='o', markersize=3, linewidth=2)
            ax.set_xlabel('Epoch')
            ax.set_ylabel('PSNR (dB)')
            ax.set_title(title)
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            self._plot_no_data(ax, title)
    
    def _plot_ssim(self, ax, history: Dict):
        """绘制SSIM"""
        title = "Structural Similarity Index" if not self.use_chinese_font else "结构相似性指数"
        if 'val_ssim' in history and len(history['val_ssim']) > 0:
            epochs = range(1, len(history['val_ssim']) + 1)
            ax.plot(epochs, history['val_ssim'], label='Val SSIM', 
                   color=self.colors['single'][1], marker='o', markersize=3, linewidth=2)
            ax.set_xlabel('Epoch')
            ax.set_ylabel('SSIM')
            ax.set_title(title)
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            self._plot_no_data(ax, title)
    
    def _plot_learning_rates(self, ax, history: Dict):
        """绘制学习率变化"""
        title = "Learning Rate Schedule" if not self.use_chinese_font else "学习率变化"
        lr_keys = ['learning_rates_g', 'learning_rates_d_hr', 'learning_rates_d_lr']
        labels = ['Generator LR', 'D_HR LR', 'D_LR LR']
        
        if all(key in history and len(history[key]) > 0 for key in lr_keys):
            for i, (key, label) in enumerate(zip(lr_keys, labels)):
                epochs = range(1, len(history[key]) + 1)
                ax.plot(epochs, history[key], label=label, 
                       color=self.colors['single'][i], alpha=0.8, linewidth=2)
            
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Learning Rate')
            ax.set_title(title)
            ax.legend()
            ax.grid(True, alpha=0.3)
            ax.set_yscale('log')
        else:
            self._plot_no_data(ax, title)
    
    def _plot_loss_comparison(self, ax, history: Dict):
        """绘制训练损失对比"""
        title = "Training Loss Comparison" if not self.use_chinese_font else "训练损失对比"
        loss_keys = ['train_g_loss', 'train_d_hr_loss', 'train_d_lr_loss']
        labels = ['Generator', 'D_HR', 'D_LR']
        
        if all(key in history and len(history[key]) > 0 for key in loss_keys):
            for i, (key, label) in enumerate(zip(loss_keys, labels)):
                epochs = range(1, len(history[key]) + 1)
                ax.plot(epochs, history[key], label=label, 
                       color=self.colors['single'][i], alpha=0.8, linewidth=2)
            
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Loss')
            ax.set_title(title)
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            self._plot_no_data(ax, title)
    
    def _plot_validation_trends(self, ax, history: Dict):
        """绘制验证损失趋势"""
        title = "Validation Loss Trends" if not self.use_chinese_font else "验证损失趋势"
        val_keys = ['val_g_loss', 'val_d_hr_loss', 'val_d_lr_loss']
        labels = ['Val G Loss', 'Val D_HR Loss', 'Val D_LR Loss']
        
        if all(key in history and len(history[key]) > 0 for key in val_keys):
            for i, (key, label) in enumerate(zip(val_keys, labels)):
                epochs = range(1, len(history[key]) + 1)
                ax.plot(epochs, history[key], label=label, 
                       color=self.colors['val'][i], alpha=0.8, linewidth=2)
            
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Loss')
            ax.set_title(title)
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            self._plot_no_data(ax, title)
    
    def _plot_train_val_curve(self, ax, history: Dict, train_key: str, val_key: str, 
                            ylabel: str, title: str, train_color: str, val_color: str):
        """绘制训练验证曲线的通用方法"""
        has_train = train_key in history and len(history[train_key]) > 0
        has_val = val_key in history and len(history[val_key]) > 0
        
        if has_train or has_val:
            if has_train:
                epochs = range(1, len(history[train_key]) + 1)
                ax.plot(epochs, history[train_key], label=f'Train', 
                       color=train_color, alpha=0.8, linewidth=2)
            
            if has_val:
                epochs = range(1, len(history[val_key]) + 1)
                ax.plot(epochs, history[val_key], label=f'Val', 
                       color=val_color, alpha=0.8, linewidth=2)
            
            ax.set_xlabel('Epoch')
            ax.set_ylabel(ylabel)
            ax.set_title(title)
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            self._plot_no_data(ax, title)
    
    def _plot_no_data(self, ax, title: str):
        """绘制无数据占位图"""
        ax.text(0.5, 0.5, 'No Data Available', 
               horizontalalignment='center', verticalalignment='center',
               transform=ax.transAxes, fontsize=12, alpha=0.6)
        ax.set_title(title)
        ax.set_xticks([])
        ax.set_yticks([])
    
    def _create_fallback_plot(self, history: Dict, timestamp: str) -> str:
        """创建简化的备用图表"""
        try:
            plt.figure(figsize=(12, 8))
            
            # 只绘制存在的数据
            subplot_idx = 1
            plot_count = 0
            
            # 检查可用的数据
            available_plots = []
            if 'train_g_loss' in history and len(history['train_g_loss']) > 0:
                available_plots.append(('train_g_loss', 'Generator Loss'))
            if 'train_d_hr_loss' in history and len(history['train_d_hr_loss']) > 0:
                available_plots.append(('train_d_hr_loss', 'D_HR Loss'))
            if 'train_d_lr_loss' in history and len(history['train_d_lr_loss']) > 0:
                available_plots.append(('train_d_lr_loss', 'D_LR Loss'))
            if 'train_cycle_loss' in history and len(history['train_cycle_loss']) > 0:
                available_plots.append(('train_cycle_loss', 'Cycle Loss'))
                
            n_plots = len(available_plots)
            if n_plots == 0:
                plt.text(0.5, 0.5, 'No training data available', 
                        horizontalalignment='center', verticalalignment='center')
                plt.title('Training Data Not Available')
            else:
                rows = (n_plots + 1) // 2
                cols = 2 if n_plots > 1 else 1
                
                for i, (key, title) in enumerate(available_plots):
                    plt.subplot(rows, cols, i + 1)
                    epochs = range(1, len(history[key]) + 1)
                    plt.plot(epochs, history[key], label=title, color=self.colors['single'][i])
                    plt.title(title)
                    plt.xlabel('Epoch')
                    plt.ylabel('Loss')
                    plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            fallback_path = os.path.join(self.save_dir, f'simple_curves_{timestamp}.png')
            plt.savefig(fallback_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logging.info(f'Fallback curves saved to: {fallback_path}')
            return fallback_path
            
        except Exception as e2:
            logging.error(f'Error creating fallback plot: {str(e2)}')
            return ""
    
    def save_training_summary(self, history: Dict, model_info: Dict, timestamp: Optional[str] = None) -> str:
        """
        保存训练摘要信息
        
        Args:
            history: 训练历史
            model_info: 模型信息
            timestamp: 时间戳
            
        Returns:
            摘要文件路径
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        summary_path = os.path.join(self.save_dir, f'training_summary_{timestamp}.txt')
        
        try:
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")
                f.write("TRAINING SUMMARY\n")
                f.write("=" * 60 + "\n\n")
                
                # 模型信息
                f.write("MODEL INFORMATION:\n")
                f.write("-" * 30 + "\n")
                for key, value in model_info.items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # 训练统计
                f.write("TRAINING STATISTICS:\n")
                f.write("-" * 30 + "\n")
                
                if 'train_g_loss' in history and len(history['train_g_loss']) > 0:
                    final_g_loss = history['train_g_loss'][-1]
                    best_g_loss = min(history['train_g_loss'])
                    f.write(f"Generator Loss - Final: {final_g_loss:.6f}, Best: {best_g_loss:.6f}\n")
                
                if 'val_psnr' in history and len(history['val_psnr']) > 0:
                    final_psnr = history['val_psnr'][-1]
                    best_psnr = max(history['val_psnr'])
                    f.write(f"PSNR - Final: {final_psnr:.4f} dB, Best: {best_psnr:.4f} dB\n")
                
                if 'val_ssim' in history and len(history['val_ssim']) > 0:
                    final_ssim = history['val_ssim'][-1]
                    best_ssim = max(history['val_ssim'])
                    f.write(f"SSIM - Final: {final_ssim:.4f}, Best: {best_ssim:.4f}\n")
                
                f.write(f"\nTotal Epochs: {len(history.get('train_g_loss', []))}\n")
                f.write(f"Summary generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            logging.info(f'Training summary saved to: {summary_path}')
            return summary_path
            
        except Exception as e:
            logging.error(f'Error saving training summary: {str(e)}')
            return ""