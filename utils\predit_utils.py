from __future__ import print_function
import os
import argparse
import torch
from torch.autograd import Variable
from PIL import Image
from torchvision.transforms import ToTensor
from glob import glob
import numpy as np
import rasterio
from rasterio.transform import Affine
from model import SRN, CycleGANNightLight


scale_factor = 4

# 图像处理模块
class ImageProcessor:
    @staticmethod
    def read_tif_file(file_path):
        """直接读取TIF文件为NumPy数组，保留原始数据"""
        with rasterio.open(file_path) as src:
            # 读取所有波段的数据
            if src.count > 1:
                # 多波段图像
                raw_data = src.read()  # 读取所有波段
                # 转置为 [高, 宽, 波段] 格式以便后续处理
                raw_data = np.transpose(raw_data, (1, 2, 0))
            else:
                # 单波段图像
                raw_data = src.read(1)  # 只读取第一个波段

            # 直接返回原始NumPy数组和元数据
            return raw_data, src.profile.copy()

class FusionModel:
    def __init__(self, model_path, use_cuda=True, scale_factor=4):
        """初始化融合模型（只加载 CycleGAN 的 LR->HR 生成器）"""
        # 1. 设备选择
        self.device = torch.device('cuda' if torch.cuda.is_available() and use_cuda else 'cpu')
        print(f"使用设备: {self.device}")

        # 2. 加载 checkpoint
        checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)

        if "G_LR_to_HR_state_dict" not in checkpoint:
            raise ValueError(f"{model_path} 中找不到 'G_LR_to_HR_state_dict'")

        # 3. 构建并加载生成器
        model = CycleGANNightLight(scale_factor=scale_factor)
        model.G_LR_to_HR.load_state_dict(checkpoint["G_LR_to_HR_state_dict"])
        self.model = model.G_LR_to_HR.to(self.device).eval()
        print("成功加载 CycleGAN 模型的 G_LR_to_HR 生成器，并切换到 eval 模式")

    def fuse_images(self, img1_tensor, img2_tensor):
        """融合两幅图像（tensor）并返回 CPU 上的结果"""
        img1 = img1_tensor.to(self.device)
        img2 = img2_tensor.to(self.device)

        with torch.no_grad():
            fused = self.model(img1, img2)

        return fused.cpu()



class MedicalImageFusion:
    def __init__(self, args):
        """初始化融合应用"""
        self.args = args
        self.processor = ImageProcessor()
        self.model = FusionModel(args.model, args.cuda )

        # 确保输出目录存在
        if not os.path.exists(args.save_folder):
            os.makedirs(args.save_folder)

    def run(self):
        """运行融合过程"""
        # 获取MS、NPP图像文件列表（优先查找TIF格式）
        file_extensions = ['*.tif']

        ms_images = []
        npp_images = []

        for ext in file_extensions:
            ms_images.extend(sorted(glob(os.path.join(self.args.MS_data_dir, ext))))
            npp_images.extend(sorted(glob(os.path.join(self.args.NPP_data_dir, ext))))

        # 确保文件列表不为空
        if not ms_images:
            raise ValueError(f"在 {self.args.MS_data_dir} 中未找到图像文件")
        if not npp_images:
            raise ValueError(f"在 {self.args.NPP_data_dir} 中未找到图像文件")

        # 处理MS-NPP双模态融合
        min_len = min(len(ms_images), len(npp_images))
        ms_images = ms_images[:min_len]
        npp_images = npp_images[:min_len]

        print(f"找到 {len(ms_images)} 个多光谱图像")
        print(f"找到 {len(npp_images)} 个NPP图像")
        print(f"处理的图像对数量: {min_len}")

        for i in range(min_len):
            # 读取图像及其元数据
            ms_profile = None
            npp_profile = None

            # 读取MS图像
            ms_img, ms_profile = self.processor.read_tif_file(ms_images[i])

            # 读取NPP图像
            npp_img, npp_profile = self.processor.read_tif_file(npp_images[i])

            # 优先使用NPP的元数据，因为它通常包含夜光数据的地理信息
            profile = npp_profile

            # 保存原始图像尺寸，用于后续调整地理坐标
            original_height = npp_img.shape[0] if len(npp_img.shape) == 2 else npp_img.shape[0]
            original_width = npp_img.shape[1] if len(npp_img.shape) == 2 else npp_img.shape[1]

            # 转换为张量 - 修正的部分
            # MS图像 (16位，3波段)
            ms_img_numpy = ms_img.astype(np.float32)
            # 确保MS图像有正确的通道维度
            if len(ms_img_numpy.shape) == 2:
                # 如果是单通道，扩展为 [H, W, 1]
                ms_img_numpy = np.expand_dims(ms_img_numpy, axis=2)

            # PyTorch需要[B, C, H, W]格式，所以需要调整维度顺序
            # 从[H, W, C]变为[C, H, W]，然后添加批次维度
            ms_tensor = torch.from_numpy(ms_img_numpy).permute(2, 0, 1).float().unsqueeze(0)

            # NPP图像处理
            npp_img_numpy = npp_img.astype(np.float32)
            if len(npp_img_numpy.shape) == 2:
                npp_img_numpy = np.expand_dims(npp_img_numpy, axis=2)

            npp_tensor = torch.from_numpy(npp_img_numpy).permute(2, 0, 1).float().unsqueeze(0)

            # 检查张量维度，确保符合模型需求
            if npp_tensor.shape[1] != 1:
                print(f"警告: NPP图像具有{npp_tensor.shape[1]}个通道，将取第一个通道")
                npp_tensor = npp_tensor[:, 0:1, :, :]

            # 融合图像 - 注意参数顺序：CycleGAN期望先NPP后MS
            fused_tensor = self.model.fuse_images(npp_tensor, ms_tensor)
            
            # 保存为TIF格式，保留原始数据类型
            output_path = os.path.join(self.args.save_folder, f"Fused_MS_NPP_{i + 1}.tif")

            # 从张量转换回numpy数组，保持原始值范围
            if fused_tensor.shape[1] == 1:
                # 单通道输出 - 通常用于夜光数据
                fused_np = fused_tensor[0][0].numpy()

                # 确定输出数据类型，优先使用NPP原始数据类型
                if profile:
                    output_dtype = profile.get('dtype', rasterio.float32)
                else:
                    output_dtype = rasterio.float32

                # 创建新的rasterio配置
                if profile:
                    # 计算尺寸比例
                    scale_height = fused_np.shape[0] / original_height
                    scale_width = fused_np.shape[1] / original_width

                    # 如果存在transform参数，则调整它
                    if 'transform' in profile:
                        old_transform = profile['transform']
                        # 创建新的transform，调整像素尺寸但保持地理范围不变
                        new_transform = Affine(
                            old_transform.a / scale_width,  # 缩小x方向的像素尺寸
                            old_transform.b,
                            old_transform.c,  # 左上角x坐标保持不变
                            old_transform.d,
                            old_transform.e / scale_height,  # 缩小y方向的像素尺寸
                            old_transform.f  # 左上角y坐标保持不变
                        )

                        # 更新profile
                        profile.update(
                            height=fused_np.shape[0],
                            width=fused_np.shape[1],
                            transform=new_transform,
                            count=1,
                            dtype=output_dtype
                        )
                    else:
                        # 如果没有transform参数，只更新基本信息
                        profile.update(
                            height=fused_np.shape[0],
                            width=fused_np.shape[1],
                            count=1,
                            dtype=output_dtype
                        )

                    with rasterio.open(output_path, 'w', **profile) as dst:
                        dst.write(fused_np.astype(profile['dtype']), 1)
                else:
                    # 如果没有原始配置，创建基本配置
                    basic_profile = {
                        'driver': 'GTiff',
                        'height': fused_np.shape[0],
                        'width': fused_np.shape[1],
                        'count': 1,
                        'dtype': output_dtype
                    }
                    with rasterio.open(output_path, 'w', **basic_profile) as dst:
                        dst.write(fused_np.astype(output_dtype), 1)
            else:
                # 多通道输出
                channels = fused_tensor.shape[1]
                fused_np = fused_tensor[0].numpy()

                # 确定输出数据类型
                if profile:
                    output_dtype = profile.get('dtype', rasterio.float32)
                else:
                    output_dtype = rasterio.float32

                # 创建rasterio配置
                if profile:
                    # 计算尺寸比例 - 注意多通道输出的维度顺序是 [C, H, W]
                    scale_height = fused_np.shape[1] / original_height
                    scale_width = fused_np.shape[2] / original_width

                    # 如果存在transform参数，则调整它
                    if 'transform' in profile:
                        old_transform = profile['transform']
                        # 创建新的transform，调整像素尺寸但保持地理范围不变
                        new_transform = Affine(
                            old_transform.a / scale_width,  # 缩小x方向的像素尺寸
                            old_transform.b,
                            old_transform.c,  # 左上角x坐标保持不变
                            old_transform.d,
                            old_transform.e / scale_height,  # 缩小y方向的像素尺寸
                            old_transform.f  # 左上角y坐标保持不变
                        )

                        # 更新profile
                        profile.update(
                            height=fused_np.shape[1],
                            width=fused_np.shape[2],
                            transform=new_transform,
                            count=channels,
                            dtype=output_dtype
                        )
                    else:
                        # 如果没有transform参数，只更新基本信息
                        profile.update(
                            height=fused_np.shape[1],
                            width=fused_np.shape[2],
                            count=channels,
                            dtype=output_dtype
                        )

                    with rasterio.open(output_path, 'w', **profile) as dst:
                        for c in range(channels):
                            dst.write(fused_np[c].astype(profile['dtype']), c + 1)
                else:
                    basic_profile = {
                        'driver': 'GTiff',
                        'height': fused_np.shape[1],
                        'width': fused_np.shape[2],
                        'count': channels,
                        'dtype': output_dtype
                    }
                    with rasterio.open(output_path, 'w', **basic_profile) as dst:
                        for c in range(channels):
                            dst.write(fused_np[c].astype(output_dtype), c + 1)

            print(f"融合完成: {output_path}")

