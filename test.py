import argparse

from utils.predit_utils import MedicalImageFusion

def main():
    # 命令行参数设置
    parser = argparse.ArgumentParser(description='夜光超分辨率重建')
    parser.add_argument('--MS_data_dir', type=str, default='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/MS',help='多光谱图像目录')
    parser.add_argument('--NPP_data_dir', type=str, default='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/NPP',help='NPP图像目录')
    parser.add_argument('--model', type=str, default='/content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth',help='模型文件路径')
    parser.add_argument('--save_folder', type=str, default='/content/drive/MyDrive/d2l-zh/SRGAN/data/out',help='输出图像保存目录')
    parser.add_argument('--cuda', action='store_true', help='使用CUDA', default=True)

    args = parser.parse_args()
    print(args)

    # 创建并运行融合应用
    fusion_app = MedicalImageFusion(args)
    fusion_app.run()
    print("收皮")


if __name__ == '__main__':
    main()
