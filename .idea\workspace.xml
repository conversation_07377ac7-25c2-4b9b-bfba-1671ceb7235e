<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="47570c84-6698-440d-ab17-04e77cecbe4f" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CoverageDataManager">
    <SUITE FILE_PATH="coverage/MSRPAN$test.coverage" NAME="test Coverage Results" MODIFIED="1569840629479" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/lapsrn_pytorch_master$data.coverage" NAME="test Coverage Results" MODIFIED="1552913899296" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/lapsrn$train.coverage" NAME="test Coverage Results" MODIFIED="1557668011141" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pfuse$train.coverage" NAME="test Coverage Results" MODIFIED="1569831491819" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pfuse$add.coverage" NAME="test Coverage Results" MODIFIED="1567495996343" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/ufuse$test.coverage" NAME="test Coverage Results" MODIFIED="1560685176491" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/ufuse$train.coverage" NAME="test Coverage Results" MODIFIED="1560691415177" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/lapsrn_pytorch_master$test.coverage" NAME="test Coverage Results" MODIFIED="1557494697646" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pfuse$average.coverage" NAME="test Coverage Results" MODIFIED="1567347530002" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pfuse$test.coverage" NAME="test Coverage Results" MODIFIED="1567421986360" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
  <component name="DatabaseView">
    <option name="SHOW_INTERMEDIATE" value="true" />
    <option name="GROUP_DATA_SOURCES" value="true" />
    <option name="GROUP_SCHEMA" value="true" />
    <option name="GROUP_CONTENTS" value="false" />
    <option name="SORT_POSITIONED" value="false" />
    <option name="SHOW_EMPTY_GROUPS" value="false" />
    <option name="AUTO_SCROLL_FROM_SOURCE" value="false" />
    <option name="HIDDEN_KINDS">
      <set />
    </option>
    <expand />
    <select />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="375">
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/test.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="303">
              <caret line="49" lean-forward="true" selection-start-line="49" selection-end-line="49" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/add.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="358">
              <caret line="49" selection-start-line="49" selection-end-line="49" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/average.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="-235">
              <caret line="50" selection-start-line="50" selection-end-line="50" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/model.py">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="1702">
              <caret line="74" selection-start-line="74" selection-end-line="74" />
              <folding>
                <element signature="e#0#12#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/mod.py" />
        <option value="$PROJECT_DIR$/train.py" />
        <option value="$PROJECT_DIR$/dataset.py" />
        <option value="$PROJECT_DIR$/model.py" />
        <option value="$PROJECT_DIR$/data.py" />
        <option value="$PROJECT_DIR$/add.py" />
        <option value="$PROJECT_DIR$/average.py" />
        <option value="$PROJECT_DIR$/test.py" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="-9" />
    <option name="y" value="-9" />
    <option name="width" value="1938" />
    <option name="height" value="1048" />
  </component>
  <component name="ProjectId" id="2wu4WwuqLJR5xll3OiwfZtR9Xr9" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="MSRPAN" type="b2602c69:ProjectViewProjectNode" />
              <item name="MSRPAN" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="MSRPAN" type="b2602c69:ProjectViewProjectNode" />
              <item name="MSRPAN" type="462c0819:PsiDirectoryNode" />
              <item name="images" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="MSRPAN" type="b2602c69:ProjectViewProjectNode" />
              <item name="MSRPAN" type="462c0819:PsiDirectoryNode" />
              <item name="result" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/MSRPAN&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="test" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="pfuse" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="47570c84-6698-440d-ab17-04e77cecbe4f" name="Default Changelist" comment="" />
      <created>1552911806190</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1552911806190</updated>
      <workItem from="1552911807810" duration="2254000" />
      <workItem from="1553044516520" duration="215000" />
      <workItem from="1556281862963" duration="3164000" />
      <workItem from="1557308560967" duration="8288000" />
      <workItem from="1557375571351" duration="613000" />
      <workItem from="1557383933530" duration="25180000" />
      <workItem from="1557448822085" duration="506000" />
      <workItem from="1557475059152" duration="7165000" />
      <workItem from="1557485508518" duration="9032000" />
      <workItem from="1557639720872" duration="23687000" />
      <workItem from="1557721834679" duration="163000" />
      <workItem from="1557723638233" duration="515000" />
      <workItem from="1557816648071" duration="990000" />
      <workItem from="1560651588102" duration="26865000" />
      <workItem from="1560685459719" duration="6210000" />
      <workItem from="1560952336697" duration="958000" />
      <workItem from="1561009695142" duration="777000" />
      <workItem from="1561010742583" duration="17000" />
      <workItem from="1562411896342" duration="5458000" />
      <workItem from="1562479577252" duration="26222000" />
      <workItem from="1562555608468" duration="1851000" />
      <workItem from="1562575044169" duration="3293000" />
      <workItem from="1562747430744" duration="322000" />
      <workItem from="1562813686607" duration="10655000" />
      <workItem from="1563694250515" duration="29347000" />
      <workItem from="1563783869876" duration="1064000" />
      <workItem from="1563797183390" duration="368000" />
      <workItem from="1563881206527" duration="196000" />
      <workItem from="1563952278221" duration="1065000" />
      <workItem from="1563955878065" duration="87000" />
      <workItem from="1563956332337" duration="50000" />
      <workItem from="1567346784151" duration="132000" />
      <workItem from="1567346937139" duration="792000" />
      <workItem from="1567348517377" duration="239000" />
      <workItem from="1567405814613" duration="21000" />
      <workItem from="1567421743216" duration="848000" />
      <workItem from="1567495005750" duration="1666000" />
      <workItem from="1567584572537" duration="520000" />
      <workItem from="1569746326269" duration="312000" />
      <workItem from="1569746652282" duration="158000" />
      <workItem from="1569746817013" duration="17000" />
      <workItem from="1569827717790" duration="3779000" />
      <workItem from="1569839940949" duration="418000" />
      <workItem from="1569840513654" duration="141000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="205739000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-9" y="-9" width="1938" height="1050" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.21620172" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.26072234" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.39954853" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="8" />
      <window_info anchor="bottom" id="Database Changes" order="9" />
      <window_info anchor="bottom" id="Event Log" order="10" side_tool="true" />
      <window_info anchor="bottom" id="Terminal" order="11" />
      <window_info anchor="bottom" id="Python Console" order="12" weight="0.32885906" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Database" order="3" weight="0.3299356" />
      <window_info anchor="right" id="SciView" order="4" weight="0.3299356" />
    </layout>
    <layout-to-restore>
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.23111111" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info active="true" anchor="bottom" id="Run" order="2" visible="true" weight="0.2738255" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="8" />
      <window_info anchor="bottom" id="Database Changes" order="9" />
      <window_info anchor="bottom" id="Event Log" order="10" side_tool="true" />
      <window_info anchor="bottom" id="Terminal" order="11" />
      <window_info anchor="bottom" id="Python Console" order="12" weight="0.32885906" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="SciView" order="3" />
      <window_info anchor="right" id="Database" order="4" />
    </layout-to-restore>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/../../../../Anaconda3/Lib/site-packages/torch/utils/data/dataloader.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="172">
          <caret line="614" selection-start-line="614" selection-end-line="614" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md" />
    <entry file="file://$PROJECT_DIR$/../../../../Anaconda3/Lib/site-packages/torch/nn/modules/module.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="216">
          <caret line="488" selection-start-line="488" selection-end-line="488" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/mod.py" />
    <entry file="file://$PROJECT_DIR$/dataset.py" />
    <entry file="file://$PROJECT_DIR$/train.py" />
    <entry file="file://$PROJECT_DIR$/data.py" />
    <entry file="file://$PROJECT_DIR$/add.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="358">
          <caret line="49" selection-start-line="49" selection-end-line="49" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/average.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-235">
          <caret line="50" selection-start-line="50" selection-end-line="50" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/model.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1702">
          <caret line="74" selection-start-line="74" selection-end-line="74" />
          <folding>
            <element signature="e#0#12#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test.py">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="303">
          <caret line="49" lean-forward="true" selection-start-line="49" selection-end-line="49" />
        </state>
      </provider>
    </entry>
  </component>
</project>