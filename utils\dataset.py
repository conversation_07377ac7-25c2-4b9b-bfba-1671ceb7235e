# --- START OF FILE dataset.py ---

import torch
from torch.utils.data import Dataset, DataLoader
import os
from glob import glob
# from torchvision.transforms import <PERSON><PERSON><PERSON><PERSON>, Compose, RandomHorizontalFlip, RandomVerticalFlip, RandomRotation # Not used directly for numpy arrays
from PIL import Image # Not strictly needed if using rasterio/cv2 for everything
import numpy as np
import rasterio  # 用于处理地理空间栅格数据
import cv2
import random
from scipy import ndimage # For rotation and gaussian_filter

class MedicalImageDataset(Dataset): # Renamed for clarity
    def __init__(self, MS_data_dir, NTL_data_dir, scale,
                 file_extension='tif', augment=True, blur_sigma_range=None): # Added blur_sigma_range

        self.MS_dir = MS_data_dir
        self.NTL_dir = NTL_data_dir
        self.scale = scale
        self.augment = augment
        self.blur_sigma_range = blur_sigma_range

        self.MS_images = sorted(glob(os.path.join(MS_data_dir, f'*.{file_extension.lower()}')) +
                                glob(os.path.join(MS_data_dir, f'*.{file_extension.upper()}')))
        self.NTL_images = sorted(glob(os.path.join(NTL_data_dir, f'*.{file_extension.lower()}')) +
                                 glob(os.path.join(NTL_data_dir, f'*.{file_extension.upper()}')))

        self.dataset_size = min(len(self.MS_images), len(self.NTL_images))

        if self.dataset_size == 0:
            raise ValueError("At least one image directory is empty or no matching files found. Please check paths.")

        if self.blur_sigma_range:
            print(f"NTL HR blur before downsampling with sigma in range: {self.blur_sigma_range}")
        else:
            print("No blur will be applied to NTL HR before downsampling.")


        if len(self.MS_images) != len(self.NTL_images):
            print(f"Warning: MS and NTL image counts do not match. Using the first {self.dataset_size} images from each.")

    def __len__(self):
        return self.dataset_size

    @staticmethod
    def read_tif_file(file_path):
        """Reads a TIF file into a NumPy array [H, W, C] or [H, W, 1] for single band."""
        with rasterio.open(file_path) as src:
            if src.count > 1:
                raw_data = src.read()  # Reads as [C, H, W]
                raw_data = np.transpose(raw_data, (1, 2, 0))  # Transpose to [H, W, C]
            else:
                raw_data = src.read(1)  # Reads as [H, W]
                raw_data = raw_data[:, :, np.newaxis] # Reshape to [H, W, 1]
            return raw_data.astype(np.float32) # Ensure float32 for processing

    def _apply_geometric_augmentation(self, img1, img2):
        """Applies the same random geometric augmentations to two images."""
        # Horizontal Flip
        if random.random() < 0.5:
            img1 = np.fliplr(img1)
            img2 = np.fliplr(img2)
        # Vertical Flip
        if random.random() < 0.5:
            img1 = np.flipud(img1)
            img2 = np.flipud(img2)
        # Rotation (0, 90, 180, 270 degrees)
        if random.random() < 0.5: # Apply rotation with 50% chance
            angle = random.choice([90, 180, 270])
            # For multi-channel images, rotate each channel or use appropriate mode
            img1 = ndimage.rotate(img1, angle, reshape=False, mode='reflect', axes=(0,1))
            img2 = ndimage.rotate(img2, angle, reshape=False, mode='reflect', axes=(0,1))
            # Ensure channel dimension is last after rotate if it squeezed
            if img1.ndim == 2: img1 = img1[:,:,np.newaxis]
            if img2.ndim == 2: img2 = img2[:,:,np.newaxis]
        return img1, img2

    def __getitem__(self, idx):
        # 1. Read pristine HR images
        ntl_hr_pristine = self.read_tif_file(self.NTL_images[idx])
        ms_hr_pristine = self.read_tif_file(self.MS_images[idx])

        # 2. Apply spatially consistent geometric augmentations (if enabled)
        # These augmented HR images will be the basis for LR generation and the HR target
        ntl_hr_augmented = ntl_hr_pristine.copy()
        ms_hr_augmented = ms_hr_pristine.copy()

        if self.augment:
            ntl_hr_augmented, ms_hr_augmented = self._apply_geometric_augmentation(ntl_hr_augmented, ms_hr_augmented)

        # The HR NTL target for the model is the (geometrically augmented) pristine image
        # Ensure it's [C, H, W] for PyTorch
        ntl_hr_target_tensor = torch.from_numpy(ntl_hr_augmented.copy().transpose(2, 0, 1)).float()


        # 3. Generate LR NTL image (Degradation: Blur -> Downsample)
        #    a. Apply Gaussian Blur (if configured)
        ntl_hr_for_lr_gen = ntl_hr_augmented.copy() # Work on a copy
        if self.blur_sigma_range:
            sigma_h = random.uniform(self.blur_sigma_range[0], self.blur_sigma_range[1])
            sigma_w = random.uniform(self.blur_sigma_range[0], self.blur_sigma_range[1])
            # Apply blur only to spatial dimensions (H, W), channel by channel if needed
            # For ndimage.gaussian_filter, sigma can be a sequence for different axes
            # If ntl_hr_for_lr_gen is [H, W, C], sigma should be (sigma_h, sigma_w, 0)
            # to not blur across channels.
            if ntl_hr_for_lr_gen.shape[2] == 1: # Single channel
                 ntl_hr_for_lr_gen_blurred = ndimage.gaussian_filter(
                     ntl_hr_for_lr_gen[:,:,0], # Process 2D slice
                     sigma=(sigma_h, sigma_w),
                     mode='reflect'
                 )[:,:,np.newaxis] # Add channel dim back
            else: # Multi-channel NTL (unlikely for typical nightlight, but robust)
                 channels = []
                 for c in range(ntl_hr_for_lr_gen.shape[2]):
                     blurred_channel = ndimage.gaussian_filter(
                         ntl_hr_for_lr_gen[:,:,c],
                         sigma=(sigma_h, sigma_w),
                         mode='reflect'
                     )
                     channels.append(blurred_channel)
                 ntl_hr_for_lr_gen_blurred = np.stack(channels, axis=-1)
            ntl_hr_for_lr_gen = ntl_hr_for_lr_gen_blurred


        #    b. Downsample
        h_hr, w_hr, _ = ntl_hr_for_lr_gen.shape
        h_lr, w_lr = h_hr // self.scale, w_hr // self.scale
        
        # Ensure cv2.resize gets a 2D or 3D array (it handles multi-channel if input is HWC)
        if ntl_hr_for_lr_gen.shape[2] == 1 and ntl_hr_for_lr_gen.ndim == 3:
            ntl_lr_np = cv2.resize(ntl_hr_for_lr_gen[:,:,0], (w_lr, h_lr), interpolation=cv2.INTER_AREA)
            ntl_lr_np = ntl_lr_np[:, :, np.newaxis] # Add channel dim back
        else: # Handles multi-channel or already 2D if squeeze happened (though we try to avoid)
            ntl_lr_np = cv2.resize(ntl_hr_for_lr_gen, (w_lr, h_lr), interpolation=cv2.INTER_AREA)
            if ntl_lr_np.ndim == 2: # If resize squeezed to 2D
                ntl_lr_np = ntl_lr_np[:, :, np.newaxis]


        ntl_lr_tensor = torch.from_numpy(ntl_lr_np.copy().transpose(2, 0, 1)).float()


        # 4. Generate LR MS image (Degradation: Just Downsample)
        #    MS image has already undergone the same geometric augmentations as NTL
        h_hr_ms, w_hr_ms, _ = ms_hr_augmented.shape # Use augmented MS
        h_lr_ms, w_lr_ms = h_hr_ms // self.scale, w_hr_ms // self.scale

        if ms_hr_augmented.shape[2] == 1 and ms_hr_augmented.ndim == 3:
            ms_lr_np = cv2.resize(ms_hr_augmented[:,:,0], (w_lr_ms, h_lr_ms), interpolation=cv2.INTER_AREA)
            ms_lr_np = ms_lr_np[:, :, np.newaxis]
        else:
            ms_lr_np = cv2.resize(ms_hr_augmented, (w_lr_ms, h_lr_ms), interpolation=cv2.INTER_AREA)
            if ms_lr_np.ndim == 2:
                 ms_lr_np = ms_lr_np[:, :, np.newaxis]

        ms_lr_tensor = torch.from_numpy(ms_lr_np.copy().transpose(2, 0, 1)).float()

        return ntl_lr_tensor,ms_lr_tensor, ntl_hr_target_tensor
