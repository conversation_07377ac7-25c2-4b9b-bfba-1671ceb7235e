import torch
import torch.nn.functional as F
from skimage.metrics import peak_signal_noise_ratio as psnr
import numpy as np
from pytorch_msssim import ssim  # Using pytorch_msssim for SSIM
# import lpips  # 暂时注释掉LPIPS，因为不适用于夜光遥感影像

class ImageMetrics:
    def __init__(self, device):
        self.device = device
        # 注释掉LPIPS初始化，因为不适用于夜光遥感影像
        # self.lpips_vgg = lpips.LPIPS(net='vgg').to(device)
        # self.lpips_vgg.eval()

    def calculate_psnr(self, pred, target):
        """Calculate PSNR between predicted and target images."""
        pred = pred.detach().cpu().numpy()
        target = target.detach().cpu().numpy()

        # 计算数据的实际范围，而不是强制clamp到[0,1]
        data_min = min(pred.min(), target.min())
        data_max = max(pred.max(), target.max())
        data_range = data_max - data_min

        # 如果数据范围太小，使用默认范围
        if data_range < 1e-6:
            data_range = 1.0

        psnr_vals = []
        for p, t in zip(pred, target):
            # 使用实际数据范围计算PSNR
            psnr_val = psnr(t, p, data_range=data_range)
            psnr_vals.append(psnr_val)

        return np.mean(psnr_vals)

    def calculate_ssim(self, pred, target):
        """Calculate SSIM between predicted and target images."""
        pred = pred.detach().cpu()
        target = target.detach().cpu()

        # 计算数据的实际范围
        data_min = min(pred.min(), target.min())
        data_max = max(pred.max(), target.max())
        data_range = data_max - data_min

        # 如果数据范围太小，使用默认范围
        if data_range < 1e-6:
            data_range = 1.0

        # 将数据归一化到[0,1]范围用于SSIM计算
        pred_norm = (pred - data_min) / data_range
        target_norm = (target - data_min) / data_range

        ssim_vals = []
        for p, t in zip(pred_norm, target_norm):
            # Ensure the images are on the same device
            p = p.unsqueeze(0).to(self.device)
            t = t.unsqueeze(0).to(self.device)

            # Calculate SSIM using pytorch_msssim with normalized data
            ssim_val = ssim(p, t, data_range=1.0, size_average=True)
            ssim_vals.append(ssim_val.item())

        return np.mean(ssim_vals)

    def calculate_nightlight_perceptual_score(self, pred, target):
        """
        计算夜光影像专用的感知质量指标
        结合梯度相似性、强度分布相似性和局部对比度
        """
        pred = pred.detach()
        target = target.detach()

        # 1. 梯度相似性 (边缘和细节保持)
        def compute_gradients(img):
            # Sobel算子计算梯度
            sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]],
                                 dtype=torch.float32, device=img.device).view(1, 1, 3, 3)
            sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]],
                                 dtype=torch.float32, device=img.device).view(1, 1, 3, 3)

            grad_x = F.conv2d(img, sobel_x, padding=1)
            grad_y = F.conv2d(img, sobel_y, padding=1)
            gradient_magnitude = torch.sqrt(grad_x**2 + grad_y**2 + 1e-8)
            return gradient_magnitude

        pred_grad = compute_gradients(pred)
        target_grad = compute_gradients(target)

        # 梯度相似性 (使用余弦相似度)
        pred_grad_flat = pred_grad.view(pred_grad.size(0), -1)
        target_grad_flat = target_grad.view(target_grad.size(0), -1)

        gradient_similarity = F.cosine_similarity(pred_grad_flat, target_grad_flat, dim=1).mean()

        # 2. 强度分布相似性 (直方图相关性)
        def compute_histogram_similarity(img1, img2, bins=64):
            # 简化的直方图相似性计算
            img1_flat = img1.view(img1.size(0), -1)
            img2_flat = img2.view(img2.size(0), -1)

            # 计算均值和标准差的相似性
            mean_diff = torch.abs(img1_flat.mean(dim=1) - img2_flat.mean(dim=1)).mean()
            std_diff = torch.abs(img1_flat.std(dim=1) - img2_flat.std(dim=1)).mean()

            # 归一化到[0,1]，差异越小相似性越高
            mean_sim = torch.exp(-mean_diff)
            std_sim = torch.exp(-std_diff)

            return (mean_sim + std_sim) / 2

        intensity_similarity = compute_histogram_similarity(pred, target)

        # 3. 局部对比度相似性
        def compute_local_contrast(img, kernel_size=5):
            # 使用卷积操作计算局部标准差作为对比度度量
            padding = kernel_size // 2

            # 创建平均池化核
            avg_kernel = torch.ones(1, 1, kernel_size, kernel_size, device=img.device) / (kernel_size * kernel_size)

            # 计算局部均值
            local_mean = F.conv2d(img, avg_kernel, padding=padding)

            # 计算局部方差
            local_mean_sq = F.conv2d(img * img, avg_kernel, padding=padding)
            local_var = local_mean_sq - local_mean * local_mean

            # 计算局部标准差（对比度）
            local_std = torch.sqrt(torch.clamp(local_var, min=1e-8))

            return local_std

        pred_contrast = compute_local_contrast(pred)
        target_contrast = compute_local_contrast(target)

        contrast_similarity = F.cosine_similarity(
            pred_contrast.view(pred_contrast.size(0), -1),
            target_contrast.view(target_contrast.size(0), -1),
            dim=1
        ).mean()

        # 综合感知质量分数 (0-1，越高越好)
        perceptual_score = (0.4 * gradient_similarity +
                          0.3 * intensity_similarity +
                          0.3 * contrast_similarity)

        return perceptual_score.item()

    def calculate_metrics(self, pred, target):
        """Calculate image metrics: PSNR, SSIM, Nightlight Perceptual Score."""
        psnr_val = self.calculate_psnr(pred, target)
        ssim_val = self.calculate_ssim(pred, target)
        perceptual_val = self.calculate_nightlight_perceptual_score(pred, target)
        return psnr_val, ssim_val, perceptual_val
