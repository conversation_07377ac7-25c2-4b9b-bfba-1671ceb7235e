import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from pytorch_msssim import ssim

# 定义损失函数
class FusionLoss(nn.Module):
    def __init__(self, lambda_rad=0.1, lambda_perc=0.1, lambda_struct=0.1):
        super(FusionLoss, self).__init__()
        self.mse_loss = nn.MSELoss()
        self.l1_loss = nn.L1Loss()

        # 权重系数
        self.lambda_rad = lambda_rad  # 辐射一致性损失权重
        self.lambda_perc = lambda_perc  # 感知损失权重
        self.lambda_struct = lambda_struct  # 结构损失权重

        # 初始化VGG网络用于感知损失
        vgg = models.vgg19(pretrained=True).features
        self.vgg_features = nn.Sequential()
        for i in range(36):  # 使用VGG前36层
            self.vgg_features.add_module(str(i), vgg[i])

        # 冻结VGG参数
        for param in self.vgg_features.parameters():
            param.requires_grad = False

    def radiation_consistency_loss(self, output, target):
        # 计算降采样后的输出与原始低分辨率图像之间的L1距离

        return self.l1_loss(output, target)

    def perceptual_loss(self, output, target):
        # 确保输入是3通道的
        if output.size(1) == 1:
            output = output.repeat(1, 3, 1, 1)
        if target.size(1) == 1:
            target = target.repeat(1, 3, 1, 1)

        # 提取特征
        output_features = self.vgg_features(output)
        target_features = self.vgg_features(target)

        # 计算特征图之间的MSE损失
        return self.mse_loss(output_features, target_features)


# 然后在structural_similarity_loss中:
    def structural_similarity_loss(self, output, target):
        return 1.0 - ssim(output, target, data_range=1.0, size_average=True)


    def forward(self, output, target):
        """
        计算总损失
        output: 模型输出的融合图像
        target: 目标高分辨率NTL图像
        """
        # 确保VGG在与输入相同的设备上
        if not next(self.vgg_features.parameters()).device == output.device:
            self.vgg_features = self.vgg_features.to(output.device)

        # 新增损失
        radiation_loss = self.radiation_consistency_loss(output, target)
        perceptual_loss = self.perceptual_loss(output, target)
        ssim_loss = self.structural_similarity_loss(output, target)

        # 总损失
        total_loss = (
                self.lambda_rad * radiation_loss +
                self.lambda_perc * perceptual_loss +
                self.lambda_struct * ssim_loss
        )

        return total_loss

class NightLightSpatialLoss(nn.Module):
    """改进的夜光影像空间细节损失 - 减少振铃效应"""
    def __init__(self, edge_weight=0.5, smooth_weight=0.3):
        super(NightLightSpatialLoss, self).__init__()
        self.l1_loss = nn.L1Loss()
        self.edge_weight = edge_weight
        self.smooth_weight = smooth_weight

        # 使用更温和的边缘检测算子
        self.register_buffer('prewitt_x', torch.tensor([
            [[-1, 0, 1], [-1, 0, 1], [-1, 0, 1]]
        ], dtype=torch.float32).unsqueeze(0))

        self.register_buffer('prewitt_y', torch.tensor([
            [[-1, -1, -1], [0, 0, 0], [1, 1, 1]]
        ], dtype=torch.float32).unsqueeze(0))

        # 高斯平滑核用于抑制噪声
        self.register_buffer('gaussian_kernel', self._get_gaussian_kernel(5, 1.0))
    
    def _get_gaussian_kernel(self, kernel_size, sigma):
        """生成高斯平滑核"""
        coords = torch.arange(kernel_size, dtype=torch.float32)
        coords -= kernel_size // 2
        g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        g = g / g.sum()
        gaussian_2d = g[:, None] * g[None, :]
        return gaussian_2d.unsqueeze(0).unsqueeze(0)
    
    def _apply_gaussian_smooth(self, x):
        """应用高斯平滑"""
        if x.size(1) == 1:
            return F.conv2d(x, self.gaussian_kernel, padding=2)
        else:
            kernel = self.gaussian_kernel.repeat(x.size(1), 1, 1, 1)
            return F.conv2d(x, kernel, padding=2, groups=x.size(1))
    
    def _compute_edges_gentle(self, x):
        """使用更温和的边缘检测"""
        # 先进行轻微平滑
        x_smooth = self._apply_gaussian_smooth(x)
        
        if x.size(1) == 1:
            edge_x = F.conv2d(x_smooth, self.prewitt_x, padding=1)
            edge_y = F.conv2d(x_smooth, self.prewitt_y, padding=1)
        else:
            prewitt_x = self.prewitt_x.repeat(x.size(1), 1, 1, 1)
            prewitt_y = self.prewitt_y.repeat(x.size(1), 1, 1, 1)
            edge_x = F.conv2d(x_smooth, prewitt_x, padding=1, groups=x.size(1))
            edge_y = F.conv2d(x_smooth, prewitt_y, padding=1, groups=x.size(1))
        
        # 使用更温和的边缘强度计算
        edge_magnitude = torch.sqrt(edge_x**2 + edge_y**2 + 1e-8)
        
        # 修复quantile函数调用 - 使用兼容的方式计算阈值
        # 方法1：使用百分位数替代quantile
        batch_size = edge_magnitude.size(0)
        edge_threshold_list = []
        
        for i in range(batch_size):
            # 对每个样本单独计算阈值
            edge_flat = edge_magnitude[i].flatten()
            # 使用kthvalue替代quantile，更兼容
            k = int(0.7 * edge_flat.numel())
            if k >= edge_flat.numel():
                k = edge_flat.numel() - 1
            threshold_val, _ = torch.kthvalue(edge_flat, k + 1)
            edge_threshold_list.append(threshold_val)
        
        # 将阈值堆叠并调整形状
        edge_threshold = torch.stack(edge_threshold_list).view(batch_size, 1, 1, 1)
        edge_threshold = edge_threshold.to(edge_magnitude.device)
        
        # 应用自适应阈值，抑制弱边缘的过度增强
        edge_mask = (edge_magnitude > edge_threshold).float()
        edge_magnitude = edge_magnitude * edge_mask
        
        return edge_magnitude
    
    def _compute_smoothness_loss(self, output, target):
        """计算平滑度损失，抑制振铃效应"""
        # 计算二阶导数（拉普拉斯算子）
        laplacian_kernel = torch.tensor([
            [[0, -1, 0], [-1, 4, -1], [0, -1, 0]]
        ], dtype=torch.float32, device=output.device).unsqueeze(0)
        
        if output.size(1) > 1:
            laplacian_kernel = laplacian_kernel.repeat(output.size(1), 1, 1, 1)
            
        output_laplacian = F.conv2d(output, laplacian_kernel, padding=1, 
                                   groups=output.size(1) if output.size(1) > 1 else 1)
        target_laplacian = F.conv2d(target, laplacian_kernel, padding=1,
                                   groups=target.size(1) if target.size(1) > 1 else 1)
        
        return self.l1_loss(output_laplacian, target_laplacian)
    
    def forward(self, output, target):
        # 1. 温和的边缘损失
        output_edges = self._compute_edges_gentle(output)
        target_edges = self._compute_edges_gentle(target)
        edge_loss = self.l1_loss(output_edges, target_edges)
        
        # 2. 平滑度损失（抑制振铃）
        smoothness_loss = self._compute_smoothness_loss(output, target)
        
        # 3. 基础像素损失
        pixel_loss = self.l1_loss(output, target)
        
        # 组合损失
        total_loss = (pixel_loss + 
                     self.edge_weight * edge_loss + 
                     self.smooth_weight * smoothness_loss)
        
        return total_loss

class MultiSpectralGuidanceLoss(nn.Module):
    """多光谱引导损失 - 利用多光谱信息指导夜光超分辨率"""
    def __init__(self):
        super(MultiSpectralGuidanceLoss, self).__init__()
        self.l1_loss = nn.L1Loss()

    def forward(self, hr_nightlight, lr_nightlight, ms_img):
        """
        Args:
            hr_nightlight: 生成的高分辨率夜光影像
            lr_nightlight: 输入的低分辨率夜光影像
            ms_img: 多光谱辅助影像
        """
        # 将高分辨率夜光下采样到低分辨率
        hr_downsampled = F.interpolate(hr_nightlight, size=lr_nightlight.shape[-2:], mode='area')

        # 基本的下采样一致性
        downsample_loss = self.l1_loss(hr_downsampled, lr_nightlight)

        # 多光谱引导的结构一致性
        # 计算多光谱图像的结构信息（使用梯度）
        ms_gray = torch.mean(ms_img, dim=1, keepdim=True)  # 转为灰度
        ms_grad_x = F.conv2d(ms_gray, torch.tensor([[-1, 0, 1]], dtype=torch.float32, device=ms_img.device).unsqueeze(0).unsqueeze(0), padding=(0,1))
        ms_grad_y = F.conv2d(ms_gray, torch.tensor([[-1], [0], [1]], dtype=torch.float32, device=ms_img.device).unsqueeze(0).unsqueeze(0), padding=(1,0))
        ms_structure = torch.sqrt(ms_grad_x**2 + ms_grad_y**2 + 1e-8)

        # 夜光影像的结构信息
        nl_grad_x = F.conv2d(hr_nightlight, torch.tensor([[-1, 0, 1]], dtype=torch.float32, device=hr_nightlight.device).unsqueeze(0).unsqueeze(0), padding=(0,1))
        nl_grad_y = F.conv2d(hr_nightlight, torch.tensor([[-1], [0], [1]], dtype=torch.float32, device=hr_nightlight.device).unsqueeze(0).unsqueeze(0), padding=(1,0))
        nl_structure = torch.sqrt(nl_grad_x**2 + nl_grad_y**2 + 1e-8)

        # 上采样多光谱结构信息到夜光分辨率
        ms_structure_up = F.interpolate(ms_structure, size=nl_structure.shape[-2:], mode='bilinear', align_corners=False)

        # 结构一致性损失
        structure_loss = self.l1_loss(nl_structure, ms_structure_up)

        return downsample_loss + 0.5 * structure_loss


class UnsupervisedSpatialConsistencyLoss(nn.Module):
    """无监督空间一致性损失 - 不依赖真实高分辨率目标"""
    def __init__(self, ms_weight=0.7, self_weight=0.3):
        super(UnsupervisedSpatialConsistencyLoss, self).__init__()
        self.l1_loss = nn.L1Loss()
        self.ms_weight = ms_weight
        self.self_weight = self_weight

        # 边缘检测算子
        self.register_buffer('sobel_x', torch.tensor([
            [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]
        ], dtype=torch.float32).unsqueeze(0))

        self.register_buffer('sobel_y', torch.tensor([
            [[-1, -2, -1], [0, 0, 0], [1, 2, 1]]
        ], dtype=torch.float32).unsqueeze(0))

    def _compute_structure(self, x):
        """计算图像结构信息"""
        if x.size(1) == 1:
            grad_x = F.conv2d(x, self.sobel_x, padding=1)
            grad_y = F.conv2d(x, self.sobel_y, padding=1)
        else:
            # 对多通道图像，先转为灰度
            x_gray = torch.mean(x, dim=1, keepdim=True)
            grad_x = F.conv2d(x_gray, self.sobel_x, padding=1)
            grad_y = F.conv2d(x_gray, self.sobel_y, padding=1)

        structure = torch.sqrt(grad_x**2 + grad_y**2 + 1e-8)
        return structure

    def _multiscale_structure_consistency(self, hr_output, ms_img):
        """多尺度结构一致性 - 利用多光谱图像的结构信息"""
        # 计算高分辨率夜光的结构
        hr_structure = self._compute_structure(hr_output)

        # 计算多光谱图像的结构
        ms_structure = self._compute_structure(ms_img)

        # 将多光谱结构上采样到夜光分辨率
        ms_structure_up = F.interpolate(ms_structure, size=hr_structure.shape[-2:],
                                       mode='bilinear', align_corners=False)

        # 结构一致性损失
        structure_loss = self.l1_loss(hr_structure, ms_structure_up)

        return structure_loss

    def _self_consistency_loss(self, hr_output, lr_input):
        """自一致性损失 - 确保生成的高分辨率图像下采样后与输入一致"""
        # 将高分辨率输出下采样到低分辨率
        hr_downsampled = F.interpolate(hr_output, size=lr_input.shape[-2:], mode='area')

        # 下采样一致性
        downsample_loss = self.l1_loss(hr_downsampled, lr_input)

        # 结构一致性 - 确保下采样后的结构与原始低分辨率结构相似
        hr_down_structure = self._compute_structure(hr_downsampled)
        lr_structure = self._compute_structure(lr_input)
        structure_consistency = self.l1_loss(hr_down_structure, lr_structure)

        return downsample_loss + 0.5 * structure_consistency

    def forward(self, hr_output, lr_input, ms_img):
        """
        Args:
            hr_output: 生成的高分辨率夜光影像
            lr_input: 输入的低分辨率夜光影像
            ms_img: 多光谱辅助影像
        """
        # 1. 多光谱引导的结构一致性
        ms_structure_loss = self._multiscale_structure_consistency(hr_output, ms_img)

        # 2. 自一致性损失
        self_consistency_loss = self._self_consistency_loss(hr_output, lr_input)

        # 组合损失
        total_loss = (self.ms_weight * ms_structure_loss +
                     self.self_weight * self_consistency_loss)

        return total_loss