#!/usr/bin/env python3
"""
快速调试张量尺寸不匹配问题
"""

import torch
import torch.nn.functional as F

def debug_tensor_operations():
    """调试可能导致尺寸不匹配的操作"""
    
    print("调试张量尺寸不匹配问题")
    print("=" * 50)
    
    # 创建测试张量 - 模拟可能出现问题的尺寸
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 测试不同尺寸组合
    test_sizes = [
        {"hr": (1, 1, 80, 80), "lr": (1, 1, 20, 20)},
        {"hr": (1, 1, 84, 84), "lr": (1, 1, 21, 21)},
        {"hr": (2, 1, 64, 64), "lr": (2, 1, 16, 16)},
    ]
    
    for i, sizes in enumerate(test_sizes):
        print(f"\n测试 {i+1}: HR{sizes['hr']}, LR{sizes['lr']}")
        
        hr_output = torch.randn(sizes['hr']).to(device)
        lr_input = torch.randn(sizes['lr']).to(device)
        
        # 测试下采样操作
        try:
            hr_downsampled = F.interpolate(hr_output, size=lr_input.shape[-2:], mode='area')
            print(f"  ✅ 下采样成功: {hr_output.shape} -> {hr_downsampled.shape}")
        except Exception as e:
            print(f"  ❌ 下采样失败: {e}")
            continue
        
        # 测试Sobel卷积
        try:
            sobel_x = torch.tensor([
                [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]
            ], dtype=torch.float32, device=device).unsqueeze(0)
            
            grad_x = F.conv2d(hr_downsampled, sobel_x, padding=1)
            print(f"  ✅ Sobel卷积成功: {hr_downsampled.shape} -> {grad_x.shape}")
        except Exception as e:
            print(f"  ❌ Sobel卷积失败: {e}")
        
        # 测试拉普拉斯卷积
        try:
            laplacian = torch.tensor([
                [[0, -1, 0], [-1, 4, -1], [0, -1, 0]]
            ], dtype=torch.float32, device=device).unsqueeze(0)
            
            lap_response = F.conv2d(hr_output, laplacian, padding=1)
            print(f"  ✅ 拉普拉斯卷积成功: {hr_output.shape} -> {lap_response.shape}")
        except Exception as e:
            print(f"  ❌ 拉普拉斯卷积失败: {e}")
        
        # 测试高斯卷积
        try:
            kernel_size = 5
            sigma = 1.0
            coords = torch.arange(kernel_size, dtype=torch.float32, device=device)
            coords -= kernel_size // 2
            g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
            g = g / g.sum()
            gaussian_2d = g[:, None] * g[None, :]
            gaussian_kernel = gaussian_2d.unsqueeze(0).unsqueeze(0)
            
            gaussian_result = F.conv2d(hr_downsampled, gaussian_kernel, padding=kernel_size//2)
            print(f"  ✅ 高斯卷积成功: {hr_downsampled.shape} -> {gaussian_result.shape}")
        except Exception as e:
            print(f"  ❌ 高斯卷积失败: {e}")

def test_unsupervised_loss_step_by_step():
    """逐步测试无监督损失函数的每个组件"""
    
    print(f"\n逐步测试无监督损失函数")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建测试数据
    hr_output = torch.randn(1, 1, 80, 80).to(device)
    lr_input = torch.randn(1, 1, 20, 20).to(device)
    
    print(f"HR输出尺寸: {hr_output.shape}")
    print(f"LR输入尺寸: {lr_input.shape}")
    
    # 1. 测试下采样一致性
    print(f"\n1. 测试下采样一致性:")
    try:
        hr_downsampled = F.interpolate(hr_output, size=lr_input.shape[-2:], mode='area')
        basic_consistency = F.l1_loss(hr_downsampled, lr_input)
        print(f"  ✅ 基本一致性损失: {basic_consistency.item():.6f}")
        
        # 梯度一致性
        sobel_x = torch.tensor([
            [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]
        ], dtype=torch.float32, device=device).unsqueeze(0)
        
        hr_grad = F.conv2d(hr_downsampled, sobel_x, padding=1)
        lr_grad = F.conv2d(lr_input, sobel_x, padding=1)
        gradient_consistency = F.l1_loss(hr_grad, lr_grad)
        print(f"  ✅ 梯度一致性损失: {gradient_consistency.item():.6f}")
        
    except Exception as e:
        print(f"  ❌ 下采样一致性失败: {e}")
        return
    
    # 2. 测试平滑度正则化
    print(f"\n2. 测试平滑度正则化:")
    try:
        laplacian = torch.tensor([
            [[0, -1, 0], [-1, 4, -1], [0, -1, 0]]
        ], dtype=torch.float32, device=device).unsqueeze(0)
        
        laplacian_response = F.conv2d(hr_output, laplacian, padding=1)
        smoothness_loss = torch.mean(torch.abs(laplacian_response))
        print(f"  ✅ 平滑度损失: {smoothness_loss.item():.6f}")
        
    except Exception as e:
        print(f"  ❌ 平滑度正则化失败: {e}")
    
    # 3. 测试频域一致性
    print(f"\n3. 测试频域一致性:")
    try:
        # 高斯模糊
        kernel_size = 5
        sigma = 1.0
        coords = torch.arange(kernel_size, dtype=torch.float32, device=device)
        coords -= kernel_size // 2
        g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
        g = g / g.sum()
        gaussian_2d = g[:, None] * g[None, :]
        gaussian_kernel = gaussian_2d.unsqueeze(0).unsqueeze(0)
        
        hr_low = F.conv2d(hr_downsampled, gaussian_kernel, padding=kernel_size//2)
        lr_low = F.conv2d(lr_input, gaussian_kernel, padding=kernel_size//2)
        
        freq_loss = F.l1_loss(hr_low, lr_low)
        print(f"  ✅ 频域一致性损失: {freq_loss.item():.6f}")
        
    except Exception as e:
        print(f"  ❌ 频域一致性失败: {e}")

if __name__ == "__main__":
    debug_tensor_operations()
    test_unsupervised_loss_step_by_step()
    print(f"\n调试完成！")
