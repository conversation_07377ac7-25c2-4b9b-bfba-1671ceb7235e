{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2326978, "status": "ok", "timestamp": 1749277796229, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "cuQA96FcoraD", "outputId": "498545d8-10b7-4fc1-d0e6-d5dc2ca288e2"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["2025-06-07 05:51:11,903 - INFO - 训练配置: {'batch_size': 16, 'epochs': 20, 'lr': 0.0005, 'd_lr': 2e-05, 'weight_decay': 1e-06, 'scheduler': 'plateau', 'lr_patience': 3, 'lr_factor': 0.5, 'min_lr': 1e-07, 'MS_data_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/train/MS', 'NTL_data_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/train/NTL', 'save_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/model', 'up_scale': 4, 'cuda': True, 'lambda_rad': 1, 'lambda_perc': 2, 'lambda_struct': 1, 'lambda_adv': 0.01, 'lambda_cycle': 1.0, 'lambda_spatial_nl': 2, 'lambda_ms_guidance': 0.01, 'use_soft_labels': True, 'use_step_scheduler': False, 'save_interval': 10, 'val_ratio': 0.1, 'seed': 42, 'n_critic': 2, 'warmup_epochs': 0}\n", "2025-06-07 05:51:11,903 - INFO - 使用设备: cuda\n", "2025-06-07 05:51:12,280 - INFO - G_LR_to_HR参数数量: 1039539\n", "2025-06-07 05:51:12,280 - INFO - G_HR_to_LR参数数量: 744276\n", "2025-06-07 05:51:12,281 - INFO - D_HR参数数量: 1555329\n", "2025-06-07 05:51:12,281 - INFO - D_LR参数数量: 1555329\n", "/usr/local/lib/python3.11/dist-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.\n", "  warnings.warn(\n", "2025-06-07 05:51:12,282 - INFO - 使用ReduceLROnPlateau学习率调度器 (因子: 0.5, 耐心值: 3)\n", "/usr/local/lib/python3.11/dist-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=VGG19_Weights.IMAGENET1K_V1`. You can also use `weights=VGG19_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n", "No blur will be applied to NTL HR before downsampling.\n", "2025-06-07 05:51:14,172 - INFO - 数据集总大小: 1600\n", "2025-06-07 05:51:14,172 - INFO - 训练集大小: 1440\n", "2025-06-07 05:51:14,172 - INFO - 验证集大小: 160\n", "2025-06-07 05:53:10,046 - INFO - Epoch [1/20], G训练: 0.0154, G验证: 0.0112, D_HR训练: 0.3183, D_HR验证: 0.2502, D_LR训练: 0.2743, D_LR验证: 0.2038, 循环损失: 0.0020, PSNR: 53.12dB, SSIM: 0.9862, 感知质量: 0.8587, 对抗分数: 0.6918, 混合评分: 0.8749, 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:55.871278\n", "2025-06-07 05:53:10,287 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth (滑动平均: 0.8749, 改进: 0.0001)\n", "2025-06-07 05:55:06,919 - INFO - Epoch [2/20], G训练: 0.0108, G验证: 0.0114, D_HR训练: 0.1840, D_HR验证: 0.1861, D_LR训练: 0.1878, D_LR验证: 0.1929, 循环损失: 0.0020, PSNR: 53.37dB, SSIM: 0.9866, 感知质量: 0.8586, 对抗分数: 0.6358, 混合评分: 0.8680, 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:56.629725\n", "2025-06-07 05:57:00,998 - INFO - Epoch [3/20], G训练: 0.0111, G验证: 0.0100, D_HR训练: 0.1851, D_HR验证: 0.1937, D_LR训练: 0.2003, D_LR验证: 0.1617, 循环损失: 0.0020, PSNR: 52.41dB, SSIM: 0.9859, 感知质量: 0.8609, 对抗分数: 0.6637, 混合评分: 0.8670, 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:54.077314\n", "2025-06-07 05:58:57,038 - INFO - Epoch [4/20], G训练: 0.0116, G验证: 0.0114, D_HR训练: 0.1874, D_HR验证: 0.1653, D_LR训练: 0.1843, D_LR验证: 0.1659, 循环损失: 0.0019, PSNR: 53.43dB, SSIM: 0.9871, 感知质量: 0.8701, 对抗分数: 0.5919, 混合评分: 0.8648, 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:56.037131\n", "2025-06-07 06:00:53,995 - INFO - Epoch [5/20], G训练: 0.0111, G验证: 0.0089, D_HR训练: 0.1860, D_HR验证: 0.2143, D_LR训练: 0.1747, D_LR验证: 0.1464, 循环损失: 0.0018, PSNR: 52.80dB, SSIM: 0.9864, 感知质量: 0.8760, 对抗分数: 0.6878, 混合评分: 0.8768, 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:56.955159\n", "2025-06-07 06:00:54,328 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth (滑动平均: 0.8768, 改进: 0.0001)\n", "2025-06-07 06:02:52,096 - INFO - Epoch [6/20], G训练: 0.0107, G验证: 0.0109, D_HR训练: 0.1975, D_HR验证: 0.1437, D_LR训练: 0.1855, D_LR验证: 0.1892, 循环损失: 0.0018, PSNR: 53.59dB, SSIM: 0.9878, 感知质量: 0.8787, 对抗分数: 0.5982, 混合评分: 0.8690, 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:57.764391\n", "2025-06-07 06:04:48,863 - INFO - Epoch [7/20], G训练: 0.0106, G验证: 0.0091, D_HR训练: 0.1936, D_HR验证: 0.1501, D_LR训练: 0.1875, D_LR验证: 0.1395, 循环损失: 0.0017, PSNR: 53.34dB, SSIM: 0.9882, 感知质量: 0.8972, 对抗分数: 0.6393, 混合评分: 0.8784, 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:56.764741\n", "2025-06-07 06:04:49,120 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth (滑动平均: 0.8784, 改进: 0.0001)\n", "2025-06-07 06:06:47,763 - INFO - Epoch [8/20], G训练: 0.0097, G验证: 0.0093, D_HR训练: 0.1865, D_HR验证: 0.2218, D_LR训练: 0.1790, D_LR验证: 0.1853, 循环损失: 0.0019, PSNR: 53.76dB, SSIM: 0.9878, 感知质量: 0.8902, 对抗分数: 0.6757, 混合评分: 0.8845, 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:58.638386\n", "2025-06-07 06:06:48,576 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth (滑动平均: 0.8845, 改进: 0.0001)\n", "2025-06-07 06:08:45,707 - INFO - Epoch [9/20], G训练: 0.0100, G验证: 0.0099, D_HR训练: 0.1693, D_HR验证: 0.2155, D_LR训练: 0.1740, D_LR验证: 0.2035, 循环损失: 0.0021, PSNR: 53.75dB, SSIM: 0.9886, 感知质量: 0.8979, 对抗分数: 0.6828, 混合评分: 0.8876, 学习率G: 2.50e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:57.127685\n", "2025-06-07 06:08:45,969 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth (滑动平均: 0.8876, 改进: 0.0001)\n", "2025-06-07 06:10:45,582 - INFO - Epoch [10/20], G训练: 0.0097, G验证: 0.0073, D_HR训练: 0.1832, D_HR验证: 0.2372, D_LR训练: 0.1772, D_LR验证: 0.1693, 循环损失: 0.0017, PSNR: 53.71dB, SSIM: 0.9880, 感知质量: 0.9018, 对抗分数: 0.6750, 混合评分: 0.8870, 学习率G: 2.50e-04, 学习率D_HR: 1.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:59.608732\n", "2025-06-07 06:12:43,621 - INFO - Epoch [11/20], G训练: 0.0102, G验证: 0.0110, D_HR训练: 0.1704, D_HR验证: 0.1440, D_LR训练: 0.1773, D_LR验证: 0.2041, 循环损失: 0.0016, PSNR: 53.30dB, SSIM: 0.9869, 感知质量: 0.8948, 对抗分数: 0.5948, 混合评分: 0.8706, 学习率G: 2.50e-04, 学习率D_HR: 1.00e-05, 学习率D_LR: 1.00e-05, 时间: 0:01:58.036721\n", "2025-06-07 06:14:39,382 - INFO - Epoch [12/20], G训练: 0.0093, G验证: 0.0086, D_HR训练: 0.1691, D_HR验证: 0.1140, D_LR训练: 0.1829, D_LR验证: 0.1660, 循环损失: 0.0016, PSNR: 53.40dB, SSIM: 0.9872, 感知质量: 0.8986, 对抗分数: 0.6309, 混合评分: 0.8776, 学习率G: 2.50e-04, 学习率D_HR: 1.00e-05, 学习率D_LR: 1.00e-05, 时间: 0:01:55.757592\n", "2025-06-07 06:16:36,932 - INFO - Epoch [13/20], G训练: 0.0092, G验证: 0.0107, D_HR训练: 0.1709, D_HR验证: 0.1980, D_LR训练: 0.1744, D_LR验证: 0.1445, 循环损失: 0.0016, PSNR: 53.24dB, SSIM: 0.9867, 感知质量: 0.9013, 对抗分数: 0.6207, 混合评分: 0.8757, 学习率G: 2.50e-04, 学习率D_HR: 1.00e-05, 学习率D_LR: 1.00e-05, 时间: 0:01:57.546128\n", "2025-06-07 06:18:35,893 - INFO - Epoch [14/20], G训练: 0.0097, G验证: 0.0090, D_HR训练: 0.1879, D_HR验证: 0.1475, D_LR训练: 0.1847, D_LR验证: 0.1624, 循环损失: 0.0016, PSNR: 53.68dB, SSIM: 0.9877, 感知质量: 0.9099, 对抗分数: 0.6360, 混合评分: 0.8829, 学习率G: 1.25e-04, 学习率D_HR: 1.00e-05, 学习率D_LR: 1.00e-05, 时间: 0:01:58.959290\n", "2025-06-07 06:20:30,417 - INFO - Epoch [15/20], G训练: 0.0097, G验证: 0.0099, D_HR训练: 0.1786, D_HR验证: 0.1480, D_LR训练: 0.1628, D_LR验证: 0.1828, 循环损失: 0.0016, PSNR: 53.19dB, SSIM: 0.9866, 感知质量: 0.9060, 对抗分数: 0.6325, 混合评分: 0.8783, 学习率G: 1.25e-04, 学习率D_HR: 1.00e-05, 学习率D_LR: 5.00e-06, 时间: 0:01:54.522359\n", "2025-06-07 06:22:24,073 - INFO - Epoch [16/20], G训练: 0.0095, G验证: 0.0095, D_HR训练: 0.1811, D_HR验证: 0.1814, D_LR训练: 0.1667, D_LR验证: 0.1892, 循环损失: 0.0015, PSNR: 53.20dB, SSIM: 0.9864, 感知质量: 0.9052, 对抗分数: 0.6344, 混合评分: 0.8784, 学习率G: 1.25e-04, 学习率D_HR: 5.00e-06, 学习率D_LR: 5.00e-06, 时间: 0:01:53.653926\n", "2025-06-07 06:24:20,025 - INFO - Epoch [17/20], G训练: 0.0092, G验证: 0.0084, D_HR训练: 0.1779, D_HR验证: 0.1492, D_LR训练: 0.1652, D_LR验证: 0.2038, 循环损失: 0.0015, PSNR: 53.49dB, SSIM: 0.9868, 感知质量: 0.9062, 对抗分数: 0.6315, 混合评分: 0.8800, 学习率G: 1.25e-04, 学习率D_HR: 5.00e-06, 学习率D_LR: 5.00e-06, 时间: 0:01:55.948380\n", "2025-06-07 06:26:11,552 - INFO - Epoch [18/20], G训练: 0.0098, G验证: 0.0084, D_HR训练: 0.1613, D_HR验证: 0.1877, D_LR训练: 0.1638, D_LR验证: 0.1596, 循环损失: 0.0015, PSNR: 53.55dB, SSIM: 0.9870, 感知质量: 0.9086, 对抗分数: 0.6473, 混合评分: 0.8833, 学习率G: 6.25e-05, 学习率D_HR: 5.00e-06, 学习率D_LR: 5.00e-06, 时间: 0:01:51.524510\n", "2025-06-07 06:28:00,030 - INFO - Epoch [19/20], G训练: 0.0098, G验证: 0.0089, D_HR训练: 0.1702, D_HR验证: 0.1999, D_LR训练: 0.1732, D_LR验证: 0.1734, 循环损失: 0.0015, PSNR: 53.29dB, SSIM: 0.9866, 感知质量: 0.9077, 对抗分数: 0.6530, 混合评分: 0.8824, 学习率G: 6.25e-05, 学习率D_HR: 5.00e-06, 学习率D_LR: 2.50e-06, 时间: 0:01:48.474944\n", "2025-06-07 06:29:48,007 - INFO - Epoch [20/20], G训练: 0.0091, G验证: 0.0093, D_HR训练: 0.1766, D_HR验证: 0.1790, D_LR训练: 0.1641, D_LR验证: 0.1981, 循环损失: 0.0015, PSNR: 53.60dB, SSIM: 0.9871, 感知质量: 0.9132, 对抗分数: 0.6172, 混合评分: 0.8803, 学习率G: 6.25e-05, 学习率D_HR: 2.50e-06, 学习率D_LR: 2.50e-06, 时间: 0:01:47.975118\n", "2025-06-07 06:29:48,257 - INFO - 训练完成！最终模型保存到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/final_model.pth\n", "2025-06-07 06:29:53,721 - INFO - Training curves saved to: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/training_curves_20250607_062948.png\n"]}], "source": ["!python /content/drive/MyDrive/d2l-zh/SRGAN/train.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 8857, "status": "ok", "timestamp": 1749278053843, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "3rPuSnZ6orQr", "outputId": "c376426f-c8d4-408f-e2cb-20df030f1149"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Namespace(MS_data_dir='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/MS', NPP_data_dir='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/NPP', model='/content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth', save_folder='/content/drive/MyDrive/d2l-zh/SRGAN/data/out', cuda=True)\n", "使用设备: cuda\n", "成功加载 CycleGAN 模型的 G_LR_to_HR 生成器，并切换到 eval 模式\n", "找到 5 个多光谱图像\n", "找到 5 个NPP图像\n", "处理的图像对数量: 5\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_1.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_2.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_3.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_4.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_5.tif\n", "收皮\n"]}], "source": ["!python /content/drive/MyDrive/d2l-zh/SRGAN/test.py"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 3732, "status": "ok", "timestamp": 1749356659567, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "hVZEBNhworA8", "outputId": "5e0dba7e-a0e6-4669-c80c-731db3308674"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting rasterio\n", "  Downloading rasterio-1.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.1 kB)\n", "Collecting affine (from rasterio)\n", "  Downloading affine-2.4.0-py3-none-any.whl.metadata (4.0 kB)\n", "Requirement already satisfied: attrs in /usr/local/lib/python3.11/dist-packages (from rasterio) (25.1.0)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from rasterio) (2025.1.31)\n", "Requirement already satisfied: click>=4.0 in /usr/local/lib/python3.11/dist-packages (from rasterio) (8.1.8)\n", "Collecting cligj>=0.5 (from rasterio)\n", "  Downloading cligj-0.7.2-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: numpy>=1.24 in /usr/local/lib/python3.11/dist-packages (from rasterio) (1.26.4)\n", "Collecting click-plugins (from rasterio)\n", "  Downloading click_plugins-1.1.1-py2.py3-none-any.whl.metadata (6.4 kB)\n", "Requirement already satisfied: pyparsing in /usr/local/lib/python3.11/dist-packages (from rasterio) (3.2.1)\n", "Downloading rasterio-1.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (22.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m22.2/22.2 MB\u001b[0m \u001b[31m103.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading cligj-0.7.2-py3-none-any.whl (7.1 kB)\n", "Downloading affine-2.4.0-py3-none-any.whl (15 kB)\n", "Downloading click_plugins-1.1.1-py2.py3-none-any.whl (7.5 kB)\n", "Installing collected packages: cligj, click-plugins, affine, rasterio\n", "Successfully installed affine-2.4.0 click-plugins-1.1.1 cligj-0.7.2 rasterio-1.4.3\n"]}], "source": ["!pip install rasterio"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 91544, "status": "ok", "timestamp": 1749356754385, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "4lEir2SAPO24", "outputId": "d333fe05-b9cd-4314-e9d3-0043c867b607"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pytorch_msssim\n", "  Downloading pytorch_msssim-1.0.0-py3-none-any.whl.metadata (8.0 kB)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (from pytorch_msssim) (2.5.1+cu124)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.17.0)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (4.12.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.4.2)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.1.5)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (2024.10.0)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********** (from torch->pytorch_msssim)\n", "  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (12.4.127)\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: triton==3.1.0 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.1.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch->pytorch_msssim) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch->pytorch_msssim) (3.0.2)\n", "Downloading pytorch_msssim-1.0.0-py3-none-any.whl (7.7 kB)\n", "Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m121.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m94.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m52.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m1.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m12.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m6.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m92.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, pytorch_msssim\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 *********\n", "    Uninstalling nvidia-cusolver-cu12-*********:\n", "      Successfully uninstalled nvidia-cusolver-cu12-*********\n", "Successfully installed nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127 pytorch_msssim-1.0.0\n"]}], "source": ["!pip install pytorch_msssim"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 33860, "status": "ok", "timestamp": 1749356799175, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "G2NzqWprZw59", "outputId": "fdcd2e78-ee3d-4d8f-9d8c-1fc08566b80e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "d-wbLVxxj2zv"}, "outputs": [], "source": ["import os\n", "\n", "try:\n", "    import gdal\n", "except:\n", "    from osgeo import gdal\n", "import numpy as np\n", "\n", "\n", "# 读取tif数据集\n", "def readTif(fileName):\n", "    dataset = gdal.Open(fileName)\n", "    if dataset is None:\n", "        print(fileName + \"文件无法打开\")\n", "    return dataset\n", "\n", "\n", "# 保存tif文件函数（改进数据类型处理和添加压缩方式）\n", "def writeTiff(im_data, im_geotrans, im_proj, path, datatype, compress='LZW'):\n", "    # 根据输入数据的维度判断波段数\n", "    if len(im_data.shape) == 3:\n", "        im_bands, im_height, im_width = im_data.shape\n", "    elif len(im_data.shape) == 2:\n", "        im_data = np.array([im_data])\n", "        im_bands, im_height, im_width = im_data.shape\n", "\n", "    # 创建文件，使用压缩选项（LZW, DEFLATE等）\n", "    driver = gdal.Get<PERSON><PERSON><PERSON><PERSON><PERSON>(\"GTiff\")\n", "    options = [\"COMPRESS={}\".format(compress)]\n", "    dataset = driver.Create(path, int(im_width), int(im_height), int(im_bands), datatype, options=options)\n", "\n", "    if dataset is not None:\n", "        dataset.SetGeoTransform(im_geotrans)  # 写入仿射变换参数\n", "        dataset.SetProjection(im_proj)  # 写入投影\n", "\n", "    # 写入每个波段\n", "    for i in range(im_bands):\n", "        dataset.GetRasterBand(i + 1).WriteArray(im_data[i])\n", "    del dataset\n", "\n", "\n", "# 像素坐标和地理坐标仿射变换\n", "def CoordTransf(Xpixel, Ypixel, GeoTransform):\n", "    XGeo = GeoTransform[0] + GeoTransform[1] * Xpixel + Ypixel * GeoTransform[2]\n", "    YGeo = GeoTransform[3] + GeoTransform[4] * Xpixel + Ypixel * GeoTransform[5]\n", "    return <PERSON><PERSON><PERSON>, YGeo\n", "\n", "\n", "# 裁剪函数，改进数据类型处理\n", "def TifCrop(Tif<PERSON>ath, SavePath, CropSize, RepetitionRate):\n", "    if not os.path.exists(SavePath):\n", "        os.makedirs(SavePath)\n", "\n", "    dataset_img = readTif(TifPath)\n", "    width = dataset_img.RasterXSize\n", "    height = dataset_img.RasterYSize\n", "    proj = dataset_img.GetProjection()\n", "    geotrans = dataset_img.GetGeoTransform()\n", "    img = dataset_img.ReadAsArray(0, 0, width, height)  # 获取数据\n", "\n", "    # 获取数据类型，保持与原始影像一致\n", "    datatype = dataset_img.GetRasterBand(1).DataType\n", "\n", "    new_name = len(os.listdir(SavePath)) + 1\n", "\n", "    for i in range(int((height - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        for j in range(int((width - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "            # 如果图像是单波段\n", "            if len(img.shape) == 2:\n", "                cropped = img[\n", "                          int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                          int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "            # 如果图像是多波段\n", "            else:\n", "                cropped = img[:,\n", "                          int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                          int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "\n", "            XGeo, YGeo = CoordTransf(int(j * CropSize * (1 - RepetitionRate)),\n", "                                     int(i * CropSize * (1 - RepetitionRate)),\n", "                                     geotrans)\n", "            crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "            writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "            new_name += 1\n", "\n", "    # 向前裁剪最后一列\n", "    for i in range(int((height - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        if len(img.shape) == 2:\n", "            cropped = img[int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                      (width - CropSize): width]\n", "        else:\n", "            cropped = img[:,\n", "                      int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                      (width - CropSize): width]\n", "\n", "        XGeo, YGeo = CoordTransf(width - CropSize, int(i * CropSize * (1 - RepetitionRate)), geotrans)\n", "        crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "        writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "        new_name += 1\n", "\n", "    # 向前裁剪最后一行\n", "    for j in range(int((width - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        if len(img.shape) == 2:\n", "            cropped = img[(height - CropSize): height,\n", "                      int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "        else:\n", "            cropped = img[:,\n", "                      (height - CropSize): height,\n", "                      int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "\n", "        XGeo, YGeo = CoordTransf(int(j * CropSize * (1 - RepetitionRate)), height - CropSize, geotrans)\n", "        crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "        writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "        new_name += 1\n", "\n", "    # 裁剪右下角\n", "    if len(img.shape) == 2:\n", "        cropped = img[(height - CropSize): height, (width - CropSize): width]\n", "    else:\n", "        cropped = img[:, (height - CropSize): height, (width - CropSize): width]\n", "\n", "    XGeo, YGeo = CoordTransf(width - CropSize, height - CropSize, geotrans)\n", "    crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "    writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "    new_name += 1\n", "\n", "\n", "# 将影像1裁剪为重复率为0.1的64×64的数据集\n", "TifCrop(r\"/content/drive/MyDrive/d2l-zh/SRGAN/data/MS.tif\", r\"/content/drive/MyDrive/d2l-zh/SRGAN/data/train/MS/\", 80, 0.5)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1tpzUV3eYIQx"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}