#!/usr/bin/env python3
"""
测试损失函数是否能正常工作，避免张量尺寸不匹配错误
"""

import torch
import torch.nn.functional as F
from utils.loss import NightLightSpatialLoss, UnsupervisedSpatialConsistencyLoss, MultiSpectralGuidanceLoss

def test_loss_functions():
    """测试所有损失函数的张量尺寸兼容性"""
    
    print("=" * 60)
    print("损失函数张量尺寸兼容性测试")
    print("=" * 60)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建测试数据 - 模拟不同尺寸的输入
    test_cases = [
        {"name": "小尺寸", "lr_size": (1, 1, 16, 16), "hr_size": (1, 1, 64, 64), "ms_size": (1, 4, 64, 64)},
        {"name": "中等尺寸", "lr_size": (2, 1, 32, 32), "hr_size": (2, 1, 128, 128), "ms_size": (2, 4, 128, 128)},
        {"name": "大尺寸", "lr_size": (1, 1, 64, 64), "hr_size": (1, 1, 256, 256), "ms_size": (1, 4, 256, 256)},
        {"name": "奇数尺寸", "lr_size": (1, 1, 21, 21), "hr_size": (1, 1, 85, 85), "ms_size": (1, 4, 85, 85)},
        {"name": "非4倍关系", "lr_size": (1, 1, 20, 20), "hr_size": (1, 1, 80, 80), "ms_size": (1, 4, 80, 80)},
    ]
    
    # 初始化损失函数
    try:
        criterion_spatial_nl = NightLightSpatialLoss().to(device)
        criterion_unsupervised = UnsupervisedSpatialConsistencyLoss().to(device)
        criterion_ms_guidance = MultiSpectralGuidanceLoss().to(device)
        print("✅ 损失函数初始化成功")
    except Exception as e:
        print(f"❌ 损失函数初始化失败: {e}")
        return
    
    # 测试每个案例
    for case in test_cases:
        print(f"\n测试案例: {case['name']}")
        print(f"  LR尺寸: {case['lr_size']}")
        print(f"  HR尺寸: {case['hr_size']}")
        print(f"  MS尺寸: {case['ms_size']}")
        
        try:
            # 创建测试张量
            lr_ntl = torch.randn(case['lr_size']).to(device)
            hr_ntl = torch.randn(case['hr_size']).to(device)
            fake_hr = torch.randn(case['hr_size']).to(device)
            ms_img = torch.randn(case['ms_size']).to(device)
            
            # 测试监督空间损失
            try:
                spatial_loss = criterion_spatial_nl(fake_hr, hr_ntl)
                print(f"  ✅ 监督空间损失: {spatial_loss.item():.6f}")
            except Exception as e:
                print(f"  ❌ 监督空间损失失败: {e}")
            
            # 测试无监督空间损失
            try:
                unsupervised_loss = criterion_unsupervised(fake_hr, lr_ntl)
                print(f"  ✅ 无监督空间损失: {unsupervised_loss.item():.6f}")
            except Exception as e:
                print(f"  ❌ 无监督空间损失失败: {e}")
            
            # 测试多光谱引导损失
            try:
                ms_loss = criterion_ms_guidance(fake_hr, lr_ntl, ms_img)
                print(f"  ✅ 多光谱引导损失: {ms_loss.item():.6f}")
            except Exception as e:
                print(f"  ❌ 多光谱引导损失失败: {e}")
                
        except Exception as e:
            print(f"  ❌ 张量创建失败: {e}")
    
    # 测试梯度计算
    print(f"\n测试梯度计算:")
    try:
        lr_ntl = torch.randn(1, 1, 32, 32, requires_grad=True).to(device)
        hr_ntl = torch.randn(1, 1, 128, 128, requires_grad=True).to(device)
        fake_hr = torch.randn(1, 1, 128, 128, requires_grad=True).to(device)
        
        # 测试监督损失的梯度
        spatial_loss = criterion_spatial_nl(fake_hr, hr_ntl)
        spatial_loss.backward(retain_graph=True)
        print("  ✅ 监督空间损失梯度计算成功")
        
        # 清零梯度
        if fake_hr.grad is not None:
            fake_hr.grad.zero_()
        
        # 测试无监督损失的梯度
        unsupervised_loss = criterion_unsupervised(fake_hr, lr_ntl)
        unsupervised_loss.backward()
        print("  ✅ 无监督空间损失梯度计算成功")
        
    except Exception as e:
        print(f"  ❌ 梯度计算失败: {e}")
    
    # 性能测试
    print(f"\n性能测试:")
    try:
        lr_ntl = torch.randn(4, 1, 64, 64).to(device)
        hr_ntl = torch.randn(4, 1, 256, 256).to(device)
        fake_hr = torch.randn(4, 1, 256, 256).to(device)
        
        import time
        
        # 监督损失性能
        start_time = time.time()
        for _ in range(10):
            loss = criterion_spatial_nl(fake_hr, hr_ntl)
        supervised_time = (time.time() - start_time) / 10
        
        # 无监督损失性能
        start_time = time.time()
        for _ in range(10):
            loss = criterion_unsupervised(fake_hr, lr_ntl)
        unsupervised_time = (time.time() - start_time) / 10
        
        print(f"  监督损失平均耗时: {supervised_time*1000:.2f}ms")
        print(f"  无监督损失平均耗时: {unsupervised_time*1000:.2f}ms")
        print(f"  性能比率: {unsupervised_time/supervised_time:.2f}x")
        
    except Exception as e:
        print(f"  ❌ 性能测试失败: {e}")
    
    print(f"\n" + "=" * 60)
    print("测试完成！")
    print("如果所有测试都通过，说明损失函数可以正常使用。")
    print("如果有失败的测试，请检查对应的错误信息。")

def test_specific_error():
    """测试特定的张量尺寸错误"""
    print("\n特定错误测试:")
    print("模拟 'tensor a (20) must match tensor b (21)' 错误")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    criterion = UnsupervisedSpatialConsistencyLoss().to(device)
    
    # 创建可能导致尺寸不匹配的张量
    problematic_sizes = [
        (1, 1, 20, 21),  # 宽度不匹配
        (1, 1, 21, 20),  # 高度不匹配
        (1, 1, 19, 19),  # 奇数尺寸
        (1, 1, 15, 17),  # 不规则尺寸
    ]
    
    for i, size in enumerate(problematic_sizes):
        print(f"  测试尺寸 {i+1}: {size}")
        try:
            hr_output = torch.randn(size).to(device)
            lr_input = torch.randn(1, 1, size[2]//4, size[3]//4).to(device)
            
            loss = criterion(hr_output, lr_input)
            print(f"    ✅ 成功，损失值: {loss.item():.6f}")
        except Exception as e:
            print(f"    ❌ 失败: {e}")

if __name__ == "__main__":
    test_loss_functions()
    test_specific_error()
