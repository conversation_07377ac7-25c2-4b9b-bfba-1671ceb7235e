#!/usr/bin/env python3
"""
对比不同空间损失函数的效果
比较监督 vs 无监督空间损失的性能
"""

import torch
import argparse
import os
import numpy as np
from utils.loss import NightLightSpatialLoss, UnsupervisedSpatialConsistencyLoss
from utils.vloss import ImageMetrics
import matplotlib.pyplot as plt

def compare_spatial_losses():
    """对比不同空间损失函数的效果"""
    
    parser = argparse.ArgumentParser(description='空间损失函数对比实验')
    parser.add_argument('--test_supervised', action='store_true', default=True, 
                       help='测试监督空间损失')
    parser.add_argument('--test_unsupervised', action='store_true', default=True,
                       help='测试无监督空间损失')
    parser.add_argument('--lambda_spatial_nl', type=float, default=2.0,
                       help='监督空间损失权重')
    parser.add_argument('--lambda_unsupervised_spatial', type=float, default=1.5,
                       help='无监督空间损失权重')
    parser.add_argument('--epochs', type=int, default=10,
                       help='对比训练轮数')
    parser.add_argument('--save_comparison', type=str, default='./loss_comparison.png',
                       help='对比结果保存路径')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("空间损失函数效果对比实验")
    print("=" * 60)
    
    # 实验配置
    experiments = []
    
    if args.test_supervised:
        experiments.append({
            'name': '监督空间损失 (lambda_spatial_nl)',
            'type': 'supervised',
            'weight': args.lambda_spatial_nl,
            'description': '直接比较生成图像与真实HR目标'
        })
    
    if args.test_unsupervised:
        experiments.append({
            'name': '无监督空间一致性损失',
            'type': 'unsupervised', 
            'weight': args.lambda_unsupervised_spatial,
            'description': '基于下采样一致性+平滑度+频域约束'
        })
    
    # 打印实验配置
    print("\n实验配置:")
    for i, exp in enumerate(experiments, 1):
        print(f"{i}. {exp['name']}")
        print(f"   权重: {exp['weight']}")
        print(f"   描述: {exp['description']}")
        print()
    
    # 训练命令生成
    print("建议的训练命令:")
    print("-" * 40)
    
    if args.test_supervised:
        print("1. 监督方式:")
        print(f"   python train.py --lambda_spatial_nl {args.lambda_spatial_nl} \\")
        print(f"                   --epochs {args.epochs} \\")
        print(f"                   --save_dir ./models/supervised_spatial")
        print()
    
    if args.test_unsupervised:
        print("2. 无监督方式:")
        print(f"   python train.py --use_unsupervised_spatial \\")
        print(f"                   --lambda_unsupervised_spatial {args.lambda_unsupervised_spatial} \\")
        print(f"                   --epochs {args.epochs} \\")
        print(f"                   --save_dir ./models/unsupervised_spatial")
        print()
    
    # 评估指标说明
    print("关键评估指标:")
    print("-" * 40)
    print("1. PSNR/SSIM: 基本图像质量指标")
    print("2. 夜光感知质量分数: 专门针对夜光数据的质量评估")
    print("3. 训练稳定性: 损失曲线的平滑程度")
    print("4. 收敛速度: 达到最佳效果所需的轮数")
    print("5. 视觉质量: 生成图像的主观质量")
    print()
    
    # 循环一致性关系分析
    print("与循环一致性的关系分析:")
    print("-" * 40)
    print("传统循环一致性:")
    print("  LR -> HR -> LR' ≈ LR (简单L1损失)")
    print()
    print("无监督空间一致性 (扩展的循环一致性):")
    print("  1. 像素级: downsample(HR) ≈ LR")
    print("  2. 梯度级: gradients(downsample(HR)) ≈ gradients(LR)")
    print("  3. 统计级: stats(downsample(HR)) ≈ stats(LR)")
    print("  4. 频域级: frequency(downsample(HR)) ≈ frequency(LR)")
    print()
    print("结论: 无监督空间损失是循环一致性的多层次细化版本")
    print()
    
    # 预期效果分析
    print("预期效果分析:")
    print("-" * 40)
    print("监督空间损失 (lambda_spatial_nl):")
    print("  ✅ 效果直接明确")
    print("  ✅ 收敛速度快")
    print("  ❌ 违背无监督原则")
    print("  ❌ 需要真实HR标注")
    print()
    print("无监督空间一致性损失:")
    print("  ✅ 完全无监督")
    print("  ✅ 理论基础扎实")
    print("  ✅ 避免多光谱噪声")
    print("  ✅ 自适应约束强度")
    print("  ⚠️ 效果可能略有下降 (5-10%)")
    print("  ⚠️ 需要更仔细的权重调优")
    print()
    
    # 权重调优建议
    print("权重调优建议:")
    print("-" * 40)
    print("如果无监督效果不如监督:")
    print("1. 增加 lambda_unsupervised_spatial: 1.5 -> 2.0 -> 2.5")
    print("2. 调整内部权重比例:")
    print("   - downsample_weight: 0.6 -> 0.7 (增强基本一致性)")
    print("   - smoothness_weight: 0.2 -> 0.15 (减少过度平滑)")
    print("   - frequency_weight: 0.2 -> 0.15 (减少频域约束)")
    print("3. 结合使用:")
    print("   - 前期用监督损失快速收敛")
    print("   - 后期切换到无监督损失精调")
    print()
    
    # 实验脚本生成
    print("自动化对比脚本:")
    print("-" * 40)
    print("# 运行对比实验")
    print("bash compare_experiments.sh")
    print()
    
    # 生成对比脚本
    script_content = f"""#!/bin/bash
# 自动化对比实验脚本

echo "开始空间损失函数对比实验..."

# 创建结果目录
mkdir -p results/supervised_spatial
mkdir -p results/unsupervised_spatial

# 监督方式训练
if [ "{args.test_supervised}" = "True" ]; then
    echo "训练监督空间损失模型..."
    python train.py --lambda_spatial_nl {args.lambda_spatial_nl} \\
                    --epochs {args.epochs} \\
                    --save_dir ./results/supervised_spatial \\
                    --log_file ./results/supervised_spatial/training.log
fi

# 无监督方式训练  
if [ "{args.test_unsupervised}" = "True" ]; then
    echo "训练无监督空间一致性模型..."
    python train.py --use_unsupervised_spatial \\
                    --lambda_unsupervised_spatial {args.lambda_unsupervised_spatial} \\
                    --epochs {args.epochs} \\
                    --save_dir ./results/unsupervised_spatial \\
                    --log_file ./results/unsupervised_spatial/training.log
fi

echo "对比实验完成！"
echo "结果保存在 ./results/ 目录下"
"""
    
    with open('compare_experiments.sh', 'w') as f:
        f.write(script_content)
    
    print("已生成 compare_experiments.sh 脚本")
    print("运行 'bash compare_experiments.sh' 开始对比实验")

if __name__ == "__main__":
    compare_spatial_losses()
