import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils import spectral_norm
import math

class ChannelAttention(nn.Module):
    def __init__(self, in_channels, reduction_ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction_ratio, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction_ratio, in_channels, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)


class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size=kernel_size, padding=kernel_size // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        out = torch.cat([avg_out, max_out], dim=1)
        out = self.conv(out)
        return self.sigmoid(out)


class DualAttentionBlock(nn.Module):
    def __init__(self, in_channels):
        super(DualAttentionBlock, self).__init__()
        self.ca = ChannelAttention(in_channels)
        self.sa = SpatialAttention()

    def forward(self, x):
        x = x * self.ca(x)
        x = x * self.sa(x)
        return x


class ResidualBlock(nn.Module):
    def __init__(self, channels):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(channels)
        self.attention = DualAttentionBlock(channels)

    def forward(self, x):
        residual = x
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out = self.attention(out)
        out += residual
        out = self.relu(out)
        return out


class MultiScaleResBlock(nn.Module):
    def __init__(self, channels):
        super(MultiScaleResBlock, self).__init__()
        self.branch1 = nn.Conv2d(channels, channels // 4, kernel_size=1, bias=False)
        self.branch2 = nn.Sequential(
            nn.Conv2d(channels, channels // 4, kernel_size=1, bias=False),
            nn.Conv2d(channels // 4, channels // 4, kernel_size=3, padding=1, bias=False)
        )
        self.branch3 = nn.Sequential(
            nn.Conv2d(channels, channels // 4, kernel_size=1, bias=False),
            nn.Conv2d(channels // 4, channels // 4, kernel_size=5, padding=2, bias=False)
        )
        self.branch4 = nn.Sequential(
            nn.Conv2d(channels, channels // 4, kernel_size=1, bias=False),
            nn.Conv2d(channels // 4, channels // 4, kernel_size=7, padding=3, bias=False)
        )
        self.conv_merge = nn.Conv2d(channels, channels, kernel_size=1, bias=False)
        self.bn = nn.BatchNorm2d(channels)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        residual = x

        b1 = self.branch1(x)
        b2 = self.branch2(x)
        b3 = self.branch3(x)
        b4 = self.branch4(x)

        out = torch.cat([b1, b2, b3, b4], dim=1)
        out = self.conv_merge(out)
        out = self.bn(out)

        out += residual
        out = self.relu(out)

        return out

class SRN(nn.Module):
    """超分辨率重建网络"""
    def __init__(self, ms_channels=3, in_channels=1, feature_channels=64, num_res_blocks=8, scale_factor=4,out_channels=1):
        super(SRN, self).__init__()

        self.scale_factor = scale_factor

        # 1. 特征提取
        self.ms_conv_initial = nn.Conv2d(ms_channels, feature_channels, kernel_size=3, padding=1)
        self.npp_conv_initial = nn.Conv2d(in_channels, feature_channels, kernel_size=3, padding=1)

        # 2. 分支特征增强
        self.ms_res_blocks = nn.Sequential(
            *[ResidualBlock(feature_channels) for _ in range(num_res_blocks // 2)]
        )
        self.npp_res_blocks = nn.Sequential(
            *[ResidualBlock(feature_channels) for _ in range(num_res_blocks // 2)]
        )
        
        # 多尺度处理模块
        self.ms_multiscale = MultiScaleResBlock(feature_channels)
        self.npp_multiscale = MultiScaleResBlock(feature_channels)

        # 3. 特征融合
        self.fusion_attention = DualAttentionBlock(feature_channels * 2)
        self.fusion_conv = nn.Conv2d(feature_channels * 2, feature_channels, kernel_size=1)
        
        # 5. 跳跃连接后的处理
        self.conv_after_skip = nn.Conv2d(feature_channels * 2, feature_channels, kernel_size=3, padding=1)
        self.relu_after_skip = nn.ReLU(inplace=True)

        # 6. 最终重建
        self.final_conv = nn.Conv2d(feature_channels, out_channels, kernel_size=3, padding=1)


        # 在 SRN 的 __init__ 方法中，统一命名
        if scale_factor < 1:  # 下采样模式 - 简化为接近真实下采样过程
          self.is_downsampling = True
          # 使用简单的下采样策略，更接近数据集的真实降质过程
          self.scale_module = nn.Sequential(
            # 轻微的特征提取
            nn.Conv2d(1, 16, 3, padding=1, bias=False),
            nn.InstanceNorm2d(16),
            nn.ReLU(inplace=True),
            # 直接下采样，模拟真实的图像获取过程
            nn.AvgPool2d(int(1/scale_factor), int(1/scale_factor)),
            # 最小的后处理
            nn.Conv2d(16, 1, 1, padding=0, bias=True)
          )
        else:  
          self.is_downsampling = False
          self.upscale = nn.Sequential(
            nn.Conv2d(feature_channels, feature_channels * 4, kernel_size=3, padding=1),
            nn.PixelShuffle(2),
            nn.ReLU(inplace=True),
            nn.Conv2d(feature_channels, feature_channels * 4, kernel_size=3, padding=1),
            nn.PixelShuffle(2),
            nn.ReLU(inplace=True))

# 然后 forward 方法就可以简化为：
    def forward(self, lm_arrs, ms_arrs=None):
        if ms_arrs is None:
          x = self.scale_module(lm_arrs)
          return x
        else:
        # 1. 初始特征提取
          ms_feat = self.ms_conv_initial(ms_arrs)
          npp_feat = self.npp_conv_initial(lm_arrs)

        # 2. 分支特征增强和多尺度处理
          ms_feat_processed = self.ms_multiscale(self.ms_res_blocks(ms_feat))
          npp_feat_processed = self.npp_multiscale(self.npp_res_blocks(npp_feat))

        # 3. 特征融合
          fused_feat_lr_space = torch.cat([ms_feat_processed, npp_feat_processed], dim=1)
          fused_feat_lr_space_att = self.fusion_attention(fused_feat_lr_space)
          fused_feat_final_lr = self.fusion_conv(fused_feat_lr_space_att)

          interpolated_npp = F.interpolate(lm_arrs, scale_factor=self.scale_factor, mode='bicubic', align_corners=False)
        
        # 使用统一的 scale_module
          x = self.upscale(fused_feat_final_lr)
            
        # 最终输出
          output = self.final_conv(x) + interpolated_npp
        
          return output

# 解决方案2：使用GroupNorm替代InstanceNorm
class Discriminator(nn.Module):
    def __init__(self, input_channels=1, ndf=64, n_layers=3):
        super(Discriminator, self).__init__()
        sequence = []
        sequence += [nn.Conv2d(input_channels, ndf, kernel_size=3, stride=2, padding=1, bias=True),nn.LeakyReLU(0.2, inplace=True)]
        nf_mult = 1
        for n in range(1, n_layers):
            nf_mult_prev = nf_mult
            nf_mult = min(2 ** n, 8)
            
            sequence += [spectral_norm(nn.Conv2d(ndf * nf_mult_prev, ndf * nf_mult, kernel_size=3, stride=2, padding=1, bias=False)),
                nn.GroupNorm(1, ndf * nf_mult),nn.LeakyReLU(0.2, inplace=True)]

        nf_mult_prev = nf_mult
        nf_mult = min(2 ** n_layers, 8)
        sequence += [spectral_norm(nn.Conv2d(ndf * nf_mult_prev, ndf * nf_mult, kernel_size=3, stride=1, padding=1, bias=False)),nn.GroupNorm(1, ndf * nf_mult),nn.LeakyReLU(0.2, inplace=True)]
        sequence += [nn.Conv2d(ndf * nf_mult, 1, kernel_size=3, stride=1, padding=1, bias=True)]        
        self.model = nn.Sequential(*sequence)
        
    def forward(self, x):
        return self.model(x)

def weights_init_normal(m):
    """权重初始化函数"""
    classname = m.__class__.__name__
    if classname.find('Conv') != -1:
        nn.init.normal_(m.weight.data, 0.0, 0.02)
        if hasattr(m, 'bias') and m.bias is not None:
            nn.init.constant_(m.bias.data, 0.0)
    elif classname.find('BatchNorm2d') != -1:
        nn.init.normal_(m.weight.data, 1.0, 0.02)
        nn.init.constant_(m.bias.data, 0.0)


class CycleGANNightLight(nn.Module):
    """循环一致性网络模型，用于夜光影像超分辨率重建"""
    def __init__(self, scale_factor=4):
        super(CycleGANNightLight, self).__init__()
        
        # 生成器: 低分辨率夜光 -> 高分辨率夜光
        self.G_LR_to_HR = SRN(in_channels=1, out_channels=1, scale_factor=scale_factor)
        
        # 生成器: 高分辨率夜光 -> 低分辨率夜光 (用于循环一致性)
        self.G_HR_to_LR = SRN(in_channels=1, out_channels=1, scale_factor=1.0/scale_factor)
        
        # 判别器: 区分真实高分辨率夜光和生成的高分辨率夜光
        self.D_HR = Discriminator(input_channels=1)
        
        # 判别器: 区分真实低分辨率夜光和生成的低分辨率夜光
        self.D_LR = Discriminator(input_channels=1)
        
        # 权重初始化
        self.apply(weights_init_normal)
    
    def forward_G_LR_to_HR(self, lr_img, ms_img=None):
        """前向传播：低分辨率到高分辨率"""
        return self.G_LR_to_HR(lr_img, ms_img)
    
    def forward_G_HR_to_LR(self, hr_img):
        """前向传播：高分辨率到低分辨率"""
        return self.G_HR_to_LR(hr_img)
    
    def forward_D_HR(self, hr_img):
        """判别器：高分辨率"""
        return self.D_HR(hr_img)
    
    def forward_D_LR(self, lr_img):
        """判别器：低分辨率"""
        return self.D_LR(lr_img)
    
    def forward(self, lm_arrs, ms_arrs=None, hr_img=None, mode='G'):
        """完整的前向传播，根据模式返回不同结果"""
        if mode == 'G':
            # 生成器模式：生成高分辨率图像
            fake_hr = self.G_LR_to_HR(lm_arrs, ms_arrs)
            return fake_hr
        elif mode == 'cycle':
            # 循环一致性模式：完整的循环 LR -> HR -> LR
            fake_hr = self.G_LR_to_HR(lm_arrs, ms_arrs)
            rec_lr = self.G_HR_to_LR(fake_hr)
            return fake_hr, rec_lr
        elif mode == 'D_HR':
            # 判别器模式：高分辨率
            return self.D_HR(hr_img)
        elif mode == 'D_LR':
            # 判别器模式：低分辨率
            return self.D_LR(lm_arrs)
        else:
            raise ValueError(f"Unsupported mode: {mode}")